#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
弈秒决完整打包脚本
打包主程序、Python模块、更新器到一个发布包
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import json

class PackageBuilder:
    def __init__(self):
        self.root_dir = Path.cwd()
        self.build_dir = self.root_dir / "build"
        self.dist_dir = self.root_dir / "dist"
        
        # 清理构建目录
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
        self.build_dir.mkdir()
        
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
        self.dist_dir.mkdir()
        
    def log(self, message):
        """打印日志"""
        print(f"[BUILD] {message}")
        
    def run_command(self, command, cwd=None):
        """执行命令"""
        self.log(f"执行命令: {command}")
        result = subprocess.run(command, shell=True, cwd=cwd or self.root_dir, 
                              capture_output=True, text=True, encoding='utf-8')
        if result.returncode != 0:
            self.log(f"命令执行失败: {result.stderr}")
            raise Exception(f"命令执行失败: {command}")
        return result.stdout
        
    def build_python_modules(self):
        """打包Python模块"""
        self.log("开始打包Python模块...")
        
        # 查找Python主程序
        python_files = list(self.root_dir.glob("*.py"))
        main_py = None
        
        for py_file in python_files:
            if py_file.name in ["main.py", "app.py", "弈秒决.py"]:
                main_py = py_file
                break
                
        if not main_py:
            # 查找包含main函数的Python文件
            for py_file in python_files:
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if 'def main(' in content or 'if __name__ == "__main__"' in content:
                            main_py = py_file
                            break
                except:
                    continue
                    
        if not main_py:
            self.log("警告: 未找到Python主程序文件")
            return None
            
        self.log(f"找到Python主程序: {main_py}")
        
        # 使用PyInstaller打包（onedir模式）
        pyinstaller_cmd = f'pyinstaller --onedir --windowed --name "弈秒决" "{main_py}"'
        
        # 添加数据文件
        if (self.root_dir / "tft_data.db").exists():
            pyinstaller_cmd += f' --add-data "tft_data.db;."'
            
        # 添加图标目录
        icon_dirs = ["英雄图标", "装备图标", "羁绊图标", "海克斯数据"]
        for icon_dir in icon_dirs:
            if (self.root_dir / icon_dir).exists():
                pyinstaller_cmd += f' --add-data "{icon_dir};{icon_dir}"'
                
        try:
            self.run_command(pyinstaller_cmd)
            
            # 复制打包结果
            pyinstaller_dist = self.root_dir / "dist" / "弈秒决"
            if pyinstaller_dist.exists():
                target_dir = self.build_dir / "弈秒决"
                shutil.copytree(pyinstaller_dist, target_dir)
                self.log(f"Python模块打包完成: {target_dir}")
                return target_dir
            else:
                self.log("PyInstaller打包失败")
                return None
                
        except Exception as e:
            self.log(f"Python打包失败: {e}")
            return None
            
    def build_updater(self):
        """构建更新器"""
        self.log("开始构建更新器...")
        
        updater_dir = self.root_dir / "updater"
        if not updater_dir.exists():
            self.log("更新器目录不存在")
            return None
            
        try:
            # 构建Tauri应用
            self.run_command("npm run tauri build", cwd=updater_dir)
            
            # 查找构建结果
            tauri_dist = updater_dir / "src-tauri" / "target" / "release"
            updater_exe = None
            
            for exe_file in tauri_dist.glob("*.exe"):
                if "updater" in exe_file.name.lower():
                    updater_exe = exe_file
                    break
                    
            if not updater_exe:
                # 查找任何exe文件
                exe_files = list(tauri_dist.glob("*.exe"))
                if exe_files:
                    updater_exe = exe_files[0]
                    
            if updater_exe:
                target_path = self.build_dir / "弈秒决更新器.exe"
                shutil.copy2(updater_exe, target_path)
                self.log(f"更新器构建完成: {target_path}")
                return target_path
            else:
                self.log("未找到更新器可执行文件")
                return None
                
        except Exception as e:
            self.log(f"更新器构建失败: {e}")
            return None
            
    def copy_external_data(self):
        """复制外置数据文件"""
        self.log("复制外置数据文件...")
        
        # 复制数据库文件
        if (self.root_dir / "tft_data.db").exists():
            shutil.copy2(self.root_dir / "tft_data.db", self.build_dir / "tft_data.db")
            self.log("复制数据库文件")
            
        # 复制图标目录
        icon_dirs = ["英雄图标", "装备图标", "羁绊图标", "海克斯数据"]
        for icon_dir in icon_dirs:
            src_dir = self.root_dir / icon_dir
            if src_dir.exists():
                dst_dir = self.build_dir / icon_dir
                shutil.copytree(src_dir, dst_dir)
                self.log(f"复制图标目录: {icon_dir}")
                
        # 复制配置文件
        config_dir = self.root_dir / "config"
        if config_dir.exists():
            dst_config = self.build_dir / "config"
            shutil.copytree(config_dir, dst_config)
            self.log("复制配置目录")
            
    def create_launcher(self):
        """创建启动器脚本"""
        self.log("创建启动器脚本...")
        
        # 创建批处理启动器
        launcher_content = '''@echo off
chcp 65001 > nul
cd /d "%~dp0"

echo 启动弈秒决更新器...
if exist "弈秒决更新器.exe" (
    start "" "弈秒决更新器.exe"
) else (
    echo 未找到更新器，直接启动主程序...
    if exist "弈秒决\\弈秒决.exe" (
        start "" "弈秒决\\弈秒决.exe"
    ) else (
        echo 未找到主程序！
        pause
    )
)
'''
        
        launcher_path = self.build_dir / "启动弈秒决.bat"
        with open(launcher_path, 'w', encoding='utf-8') as f:
            f.write(launcher_content)
            
        self.log(f"启动器创建完成: {launcher_path}")
        
    def create_readme(self):
        """创建说明文件"""
        readme_content = '''# 弈秒决 TFT助手

## 目录结构
- 启动弈秒决.bat - 主启动器（推荐使用）
- 弈秒决更新器.exe - 更新器程序
- 弈秒决/ - 主程序目录
- tft_data.db - 游戏数据库
- 英雄图标/ - 英雄头像
- 装备图标/ - 装备图标
- 羁绊图标/ - 羁绊图标
- 海克斯数据/ - 海克斯强化数据
- config/ - 配置文件

## 使用说明
1. 双击"启动弈秒决.bat"启动程序
2. 首次运行会自动检查更新
3. 如有更新会自动下载并安装
4. 更新完成后自动启动主程序

## 手动启动
如果启动器有问题，可以直接运行：
- 弈秒决更新器.exe - 检查更新并启动
- 弈秒决/弈秒决.exe - 直接启动主程序

## 注意事项
- 请保持网络连接以获取最新更新
- 不要删除或移动数据文件
- 如有问题请联系开发者
'''
        
        readme_path = self.build_dir / "README.txt"
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
            
        self.log(f"说明文件创建完成: {readme_path}")
        
    def create_final_package(self):
        """创建最终发布包"""
        self.log("创建最终发布包...")
        
        # 创建版本信息
        version_info = {
            "version": "1.0.0",
            "build_date": str(Path().cwd()),
            "components": {
                "main_app": "弈秒决主程序",
                "updater": "弈秒决更新器",
                "data": "游戏数据文件"
            }
        }
        
        version_path = self.build_dir / "version_info.json"
        with open(version_path, 'w', encoding='utf-8') as f:
            json.dump(version_info, f, indent=2, ensure_ascii=False)
            
        # 创建压缩包
        import zipfile
        
        zip_path = self.dist_dir / "弈秒决_完整版.zip"
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in self.build_dir.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(self.build_dir)
                    zipf.write(file_path, arcname)
                    
        self.log(f"发布包创建完成: {zip_path}")
        return zip_path
        
    def build_all(self):
        """执行完整构建"""
        self.log("开始完整构建...")
        
        try:
            # 1. 打包Python模块
            python_result = self.build_python_modules()
            
            # 2. 构建更新器
            updater_result = self.build_updater()
            
            # 3. 复制外置数据
            self.copy_external_data()
            
            # 4. 创建启动器
            self.create_launcher()
            
            # 5. 创建说明文件
            self.create_readme()
            
            # 6. 创建最终发布包
            final_package = self.create_final_package()
            
            self.log("构建完成！")
            self.log(f"构建目录: {self.build_dir}")
            self.log(f"发布包: {final_package}")
            
            return True
            
        except Exception as e:
            self.log(f"构建失败: {e}")
            return False

if __name__ == "__main__":
    builder = PackageBuilder()
    success = builder.build_all()
    
    if success:
        print("\n✓ 构建成功！")
        print(f"构建目录: {builder.build_dir}")
        print(f"发布包: {builder.dist_dir}")
    else:
        print("\n✗ 构建失败！")
        sys.exit(1)
