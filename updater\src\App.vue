<template>
  <div class="updater-container">
    <!-- 头部 -->
    <div class="header">
      <h1>弈秒决更新器</h1>
      <p>检查并更新游戏数据和图标资源</p>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading"></div>
        <span>正在检查更新...</span>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <p>❌ {{ error }}</p>
        <button @click="checkUpdates" class="btn btn-primary">重试</button>
      </div>

      <!-- 更新列表 -->
      <div v-else>
        <!-- 组件状态列表 -->
        <div class="status-list">
          <div v-for="component in components" :key="component.id" class="status-item">
            <div class="status-info">
              <div class="status-name">{{ component.name }}</div>
              <div class="status-version">
                {{ component.currentVersion }}
                <span v-if="component.hasUpdate"> → {{ component.latestVersion }}</span>
              </div>
              <div class="status-description">{{ component.description }}</div>
            </div>
            <div class="status-right">
              <div class="status-size">{{ formatFileSize(component.size) }}</div>
              <span 
                :class="['status-badge', component.hasUpdate ? 'status-update' : 'status-latest']"
              >
                {{ component.hasUpdate ? '有更新' : '最新' }}
              </span>
            </div>
          </div>
        </div>

        <!-- 更新进度 -->
        <div v-if="updateProgress.length > 0" class="progress-container">
          <div v-for="progress in updateProgress" :key="progress.componentId" class="progress-item">
            <div class="progress-header">
              <span>{{ progress.componentName }}</span>
              <span class="progress-status">{{ progress.message }}</span>
            </div>
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: progress.progress + '%' }"
                :class="{ 'progress-error': progress.status === 'error' }"
              ></div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="actions">
          <button
            v-if="hasUpdates && !isUpdating"
            @click="startUpdate"
            class="btn btn-primary"
          >
            一键更新 ({{ updateCount }} 个组件)
          </button>

          <button
            v-else-if="!hasUpdates && !isLoading"
            @click="launchMainApp"
            class="btn btn-success"
            :disabled="isLaunching"
          >
            {{ isLaunching ? '启动中...' : '启动弈秒决' }}
          </button>

          <button
            v-else-if="!hasUpdates"
            @click="checkUpdates"
            class="btn btn-secondary"
          >
            重新检查
          </button>

          <button
            v-else
            class="btn btn-primary"
            disabled
          >
            <div class="loading"></div>
            更新中...
          </button>
        </div>

        <!-- 最后检查时间 -->
        <div class="last-check">
          最后检查: {{ lastCheckTime }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { updateService } from '@/services/updateService'
import type { ComponentStatus, UpdateProgress, UpdateManifest } from '@/types'

// 响应式数据
const loading = ref(true)
const error = ref('')
const components = ref<ComponentStatus[]>([])
const updateProgress = ref<UpdateProgress[]>([])
const isUpdating = ref(false)
const isLaunching = ref(false)
const lastCheckTime = ref('')
const remoteManifest = ref<UpdateManifest | null>(null)

// 计算属性
const hasUpdates = computed(() => 
  components.value.some(c => c.hasUpdate)
)

const updateCount = computed(() => 
  components.value.filter(c => c.hasUpdate).length
)

// 方法
const checkUpdates = async () => {
  loading.value = true
  error.value = ''
  
  try {
    const [statuses, manifest] = await Promise.all([
      updateService.checkUpdates(),
      updateService.getRemoteManifest()
    ])
    
    components.value = statuses
    remoteManifest.value = manifest
    lastCheckTime.value = new Date().toLocaleString('zh-CN')
  } catch (err) {
    error.value = err instanceof Error ? err.message : '检查更新失败'
  } finally {
    loading.value = false
  }
}

const startUpdate = async () => {
  if (!remoteManifest.value) return
  
  isUpdating.value = true
  updateProgress.value = []
  
  const componentsToUpdate = components.value.filter(c => c.hasUpdate)
  
  for (const component of componentsToUpdate) {
    try {
      await updateService.downloadComponent(
        component.id,
        remoteManifest.value,
        (progress) => {
          const existingIndex = updateProgress.value.findIndex(p => p.componentId === progress.componentId)
          if (existingIndex >= 0) {
            updateProgress.value[existingIndex] = progress
          } else {
            updateProgress.value.push(progress)
          }
        }
      )
      
      // 更新本地版本信息
      const remoteComponent = remoteManifest.value.components[component.id]
      await updateService.updateLocalVersion(
        component.id, 
        remoteComponent.version, 
        remoteComponent.checksum
      )
      
      // 更新组件状态
      const componentIndex = components.value.findIndex(c => c.id === component.id)
      if (componentIndex >= 0) {
        components.value[componentIndex].currentVersion = remoteComponent.version
        components.value[componentIndex].hasUpdate = false
      }
      
    } catch (err) {
      console.error(`更新组件 ${component.id} 失败:`, err)
    }
  }
  
  isUpdating.value = false
}

const formatFileSize = (bytes: number): string => {
  return updateService.formatFileSize(bytes)
}

// 启动主程序
const launchMainApp = async () => {
  isLaunching.value = true
  try {
    await updateService.launchMainApplication()
    // 启动成功后可以关闭更新器
    // 这里可以添加关闭窗口的逻辑
  } catch (err) {
    error.value = err instanceof Error ? err.message : '启动失败'
  } finally {
    isLaunching.value = false
  }
}

// 生命周期
onMounted(() => {
  checkUpdates()
})
</script>
