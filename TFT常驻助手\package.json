{"name": "tft-assistant", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "dev:optimized": "powershell -ExecutionPolicy Bypass -File dev-optimized.ps1", "build": "run-p type-check \"build-only {@}\" --", "build:release": "powershell -ExecutionPolicy Bypass -File build-release.ps1", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build"}, "dependencies": {"@tauri-apps/api": "^2.7.0", "@tauri-apps/plugin-shell": "^2.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.525.0", "pinia": "^3.0.3", "radix-vue": "^1.9.17", "tailwind-merge": "^3.3.1", "vue": "^3.5.17"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@tauri-apps/cli": "^2.7.1", "@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vitest/eslint-plugin": "^1.2.7", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "npm-run-all2": "^8.0.4", "postcss": "^8.5.6", "prettier": "3.5.3", "tailwindcss": "^4.1.11", "terser": "^5.43.1", "typescript": "~5.8.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4", "vue-tsc": "^2.2.10"}}