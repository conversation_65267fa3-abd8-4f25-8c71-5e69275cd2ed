import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { useDataStore } from './stores/data'
import { invoke } from '@tauri-apps/api/core'
import { getSmartIconPath } from './utils/iconUtils'
import { getCurrentWindow } from '@tauri-apps/api/window'

import App from './App.vue'
import './assets/index.css'

// 预加载海克斯图标函数
const preloadHexIcons = async (hexes: any[]) => {
  console.log(`🖼️ 主应用预加载${hexes.length}个海克斯图标...`)
  try {
    await Promise.allSettled(
      hexes.map(hex => {
        if (hex.icon_path) {
          return getSmartIconPath(hex.icon_path)
        }
        return Promise.resolve(null)
      })
    )
    console.log(`✅ 主应用海克斯图标预加载完成`)
  } catch (error) {
    console.warn('海克斯图标预加载失败:', error)
  }
}

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)

// 立即挂载应用到DOM，但窗口仍然隐藏
app.mount('#app')

// 延迟显示窗口，避免白屏问题
setTimeout(async () => {
  try {
    console.log('🪟 显示应用窗口...')
    const appWindow = getCurrentWindow()
    await appWindow.show()
    await appWindow.setFocus()
    console.log('✅ 窗口显示完成')
  } catch (error) {
    console.error('❌ 显示窗口失败:', error)
  }
}, 100) // 优化：100ms后显示窗口，更快响应

// 优化：分阶段数据预加载，更快响应
// 第一阶段：窗口显示后立即开始关键数据预加载
setTimeout(async () => {
  try {
    console.log('🚀 开始第一阶段数据预加载（关键数据）...')
    const dataStore = useDataStore()

    // 优先加载英雄数据（最常用）
    const heroResults = await invoke('get_hero_list')
    if (heroResults && !(heroResults as any).error) {
      const heroData = (heroResults as any).data || heroResults
      dataStore.setCachedQuery('hero_list', heroData)
      console.log(`✅ 英雄数据预加载完成，共${heroData.length}条`)
    }
  } catch (error) {
    console.error('❌ 第一阶段数据预加载失败:', error)
  }
}, 300) // 300ms后开始关键数据预加载

// 第二阶段：后台预加载其他数据
setTimeout(async () => {
  try {
    console.log('🚀 开始第二阶段数据预加载（其他数据）...')
    const dataStore = useDataStore()

    // 并行加载装备和海克斯数据
    const [itemResults, hexResults] = await Promise.all([
      invoke('get_item_list'),
      invoke('get_hex_list')
    ])

    if (itemResults && !(itemResults as any).error) {
      const itemData = (itemResults as any).data || itemResults
      dataStore.setCachedQuery('item_list', itemData)
      console.log(`✅ 装备数据预加载完成，共${itemData.length}条`)
    } else {
      console.error('❌ 装备数据预加载失败:', itemResults)
    }

    if (hexResults && !(hexResults as any).error) {
      const hexData = (hexResults as any).data || hexResults
      dataStore.setCachedQuery('hex_list_fast', hexData)
      console.log(`✅ 海克斯数据预加载完成，共${hexData.length}条`)

      // 预加载前20个海克斯图标
      setTimeout(() => {
        preloadHexIcons(hexData.slice(0, 20))
      }, 100)
    } else {
      console.error('❌ 海克斯数据预加载失败:', hexResults)
    }

    console.log('🎉 所有数据预加载完成，后续访问将实现零延迟！')

    // 验证缓存是否正确设置
    setTimeout(() => {
      console.log('🔍 验证缓存状态:')
      console.log('- 英雄缓存:', !!dataStore.getCachedQuery('hero_list'))
      console.log('- 装备缓存:', !!dataStore.getCachedQuery('item_list'))
      console.log('- 海克斯缓存:', !!dataStore.getCachedQuery('hex_list_fast'))
      console.log('- 所有缓存键:', Object.keys(dataStore.queryCache))
    }, 500)
  } catch (error) {
    console.error('❌ 第二阶段数据预加载失败:', error)
  }
}, 600) // 600ms后开始其他数据预加载
