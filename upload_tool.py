#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
弈秒决资源上传工具
自动打包、计算哈希、上传资源文件到七牛云
"""

import os
import sys
import json
import hashlib
import zipfile
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from pathlib import Path
import threading
from datetime import datetime
from qiniu import Auth, put_file, BucketManager
import requests

class UploadTool:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("弈秒决资源上传工具")
        self.root.geometry("800x600")
        
        # 七牛云配置
        self.access_key = os.getenv('QINIU_ACCESS_KEY')
        self.secret_key = os.getenv('QINIU_SECRET_KEY')
        self.bucket_name = os.getenv('QINIU_BUCKET_NAME', 'yuxianglei')
        self.domain = os.getenv('QINIU_DOMAIN', 'https://download.yuxianglei.com')
        
        if not self.access_key or not self.secret_key:
            messagebox.showerror("错误", "请设置环境变量 QINIU_ACCESS_KEY 和 QINIU_SECRET_KEY")
            sys.exit(1)

        self.auth = Auth(self.access_key, self.secret_key)
        self.bucket = BucketManager(self.auth)

        # 验证配置
        self.verify_config()
        
        # 当前工作目录
        self.work_dir = Path.cwd()
        
        # 资源配置 - 建议全部作为压缩包上传
        self.resources = {
            'database': {
                'name': '游戏数据库',
                'local_path': 'tft_data.db',
                'remote_filename': 'tft_data.db',
                'description': 'TFT游戏数据库文件',
                'compress': False  # 数据库文件不压缩
            },
            'hero_icons': {
                'name': '英雄图标',
                'local_path': '英雄图标',
                'remote_filename': 'hero_icons.zip',
                'description': '英雄头像图标包',
                'compress': True  # 目录压缩为ZIP
            },
            'item_icons': {
                'name': '装备图标',
                'local_path': '装备图标',
                'remote_filename': 'item_icons.zip',
                'description': '装备图标包',
                'compress': True
            },
            'trait_icons': {
                'name': '羁绊图标',
                'local_path': '羁绊图标',
                'remote_filename': 'trait_icons.zip',
                'description': '羁绊图标包',
                'compress': True
            },
            'hex_icons': {
                'name': '海克斯数据',
                'local_path': '海克斯数据',
                'remote_filename': 'hex_data.zip',
                'description': '海克斯强化数据包',
                'compress': True
            }
        }
        
        self.setup_ui()
        
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="弈秒决资源上传工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 工作目录显示
        ttk.Label(main_frame, text="工作目录:").grid(row=1, column=0, sticky=tk.W)
        self.dir_label = ttk.Label(main_frame, text=str(self.work_dir), foreground="blue")
        self.dir_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # 选择目录按钮
        ttk.Button(main_frame, text="选择目录", command=self.select_directory).grid(row=2, column=0, pady=10, sticky=tk.W)
        
        # 资源列表
        ttk.Label(main_frame, text="资源文件:", font=("Arial", 12, "bold")).grid(row=3, column=0, sticky=tk.W, pady=(20, 5))
        
        # 创建资源列表框架
        list_frame = ttk.Frame(main_frame)
        list_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 资源列表
        columns = ('资源', '本地路径', '状态', '大小')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)
        
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=150)
            
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="扫描资源", command=self.scan_resources).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="上传全部", command=self.upload_all).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="生成版本文件", command=self.generate_version_file).pack(side=tk.LEFT, padx=5)
        
        # 日志区域
        ttk.Label(main_frame, text="操作日志:", font=("Arial", 12, "bold")).grid(row=6, column=0, sticky=tk.W, pady=(20, 5))
        
        self.log_text = scrolledtext.ScrolledText(main_frame, height=10, width=80)
        self.log_text.grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(7, weight=1)
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 初始扫描
        self.scan_resources()

    def verify_config(self):
        """验证七牛云配置"""
        try:
            # 获取bucket列表来验证配置
            _, _, info = self.bucket.list(self.bucket_name, limit=1)
            if info.status_code == 200:
                self.log(f"七牛云配置验证成功，bucket: {self.bucket_name}")
            elif info.status_code == 631:
                # bucket不存在，列出可用的bucket
                self.log("指定的bucket不存在，正在获取可用bucket列表...")
                buckets_ret, buckets_info = self.bucket.buckets()
                if buckets_info.status_code == 200:
                    bucket_names = [bucket['id'] for bucket in buckets_ret]
                    self.log(f"可用的bucket: {bucket_names}")
                    if bucket_names:
                        # 使用第一个可用的bucket
                        self.bucket_name = bucket_names[0]
                        self.log(f"自动切换到bucket: {self.bucket_name}")
                    else:
                        raise Exception("没有可用的bucket")
                else:
                    raise Exception(f"获取bucket列表失败: {buckets_info.text_body}")
            else:
                raise Exception(f"验证失败: {info.text_body}")
        except Exception as e:
            self.log(f"七牛云配置验证失败: {e}")
            messagebox.showwarning("配置警告", f"七牛云配置可能有问题: {e}")

    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()
        
    def select_directory(self):
        """选择工作目录"""
        directory = filedialog.askdirectory(initialdir=self.work_dir)
        if directory:
            self.work_dir = Path(directory)
            self.dir_label.config(text=str(self.work_dir))
            self.scan_resources()
            
    def scan_resources(self):
        """扫描资源文件"""
        self.log("开始扫描资源文件...")
        
        # 清空列表
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        for res_id, config in self.resources.items():
            local_path = self.work_dir / config['local_path']
            
            if local_path.exists():
                if local_path.is_file():
                    size = self.format_size(local_path.stat().st_size)
                    status = "文件存在"
                else:
                    # 目录，计算总大小
                    total_size = sum(f.stat().st_size for f in local_path.rglob('*') if f.is_file())
                    size = self.format_size(total_size)
                    status = "目录存在"
            else:
                size = "0 B"
                status = "不存在"
                
            self.tree.insert('', tk.END, values=(
                config['name'],
                config['local_path'],
                status,
                size
            ))
            
        self.log("资源扫描完成")
        
    def format_size(self, bytes_size):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_size < 1024:
                return f"{bytes_size:.1f} {unit}"
            bytes_size /= 1024
        return f"{bytes_size:.1f} TB"
        
    def calculate_md5(self, file_path):
        """计算文件MD5"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
        
    def create_zip(self, source_dir, zip_path):
        """创建ZIP文件"""
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in source_dir.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(source_dir)
                    zipf.write(file_path, arcname)
                    
    def upload_file(self, local_path, remote_key):
        """上传文件到七牛云"""
        token = self.auth.upload_token(self.bucket_name, remote_key)
        ret, info = put_file(token, remote_key, str(local_path))
        
        if info.status_code == 200:
            return True, ret
        else:
            return False, info.text_body
            
    def upload_all(self):
        """上传所有资源"""
        def upload_thread():
            try:
                self.log("开始上传资源...")
                
                for res_id, config in self.resources.items():
                    local_path = self.work_dir / config['local_path']
                    
                    if not local_path.exists():
                        self.log(f"跳过 {config['name']}: 文件不存在")
                        continue
                        
                    self.log(f"处理 {config['name']}...")
                    
                    # 准备上传文件
                    if config.get('compress', False) and local_path.is_dir():
                        # 目录需要打包为ZIP
                        zip_path = self.work_dir / f"temp_{config['remote_filename']}"
                        self.log(f"  打包目录到 {zip_path.name}")
                        self.create_zip(local_path, zip_path)
                        upload_path = zip_path
                    else:
                        # 直接上传文件（数据库等）
                        upload_path = local_path
                        
                    # 上传文件
                    self.log(f"  上传到七牛云...")
                    success, result = self.upload_file(upload_path, config['remote_filename'])
                    
                    if success:
                        self.log(f"  ✓ {config['name']} 上传成功")
                    else:
                        self.log(f"  ✗ {config['name']} 上传失败: {result}")
                        
                    # 清理临时文件
                    if local_path.is_dir() and zip_path.exists():
                        zip_path.unlink()
                        
                self.log("所有资源上传完成！")
                
            except Exception as e:
                self.log(f"上传过程中出错: {str(e)}")
                
        # 在新线程中执行上传
        threading.Thread(target=upload_thread, daemon=True).start()
        
    def generate_version_file(self):
        """生成版本文件"""
        def generate_thread():
            try:
                self.log("生成版本文件...")
                
                version_data = {
                    "version": "1.0.0",
                    "last_updated": datetime.now().isoformat() + "Z",
                    "download_base_url": f"{self.domain}/",
                    "components": {}
                }
                
                for res_id, config in self.resources.items():
                    local_path = self.work_dir / config['local_path']
                    
                    if not local_path.exists():
                        continue
                        
                    # 准备文件用于计算哈希
                    if local_path.is_dir():
                        zip_path = self.work_dir / f"temp_{config['remote_filename']}"
                        self.create_zip(local_path, zip_path)
                        calc_path = zip_path
                    else:
                        calc_path = local_path
                        
                    # 计算文件信息
                    file_size = calc_path.stat().st_size
                    checksum = self.calculate_md5(calc_path)
                    
                    version_data["components"][res_id] = {
                        "name": config['name'],
                        "version": datetime.now().strftime("%Y%m%d.1"),
                        "local_path": config['local_path'],
                        "remote_filename": config['remote_filename'],
                        "checksum": checksum,
                        "checksum_type": "md5",
                        "size": file_size,
                        "description": config['description']
                    }
                    
                    # 清理临时文件
                    if local_path.is_dir() and zip_path.exists():
                        zip_path.unlink()
                        
                    self.log(f"  ✓ {config['name']}: {checksum}")
                
                # 保存版本文件
                version_file = self.work_dir / "version.json"
                with open(version_file, 'w', encoding='utf-8') as f:
                    json.dump(version_data, f, indent=2, ensure_ascii=False)
                    
                self.log(f"版本文件已保存到: {version_file}")
                
                # 上传版本文件
                self.log("上传版本文件到七牛云...")
                success, result = self.upload_file(version_file, "version.json")
                
                if success:
                    self.log("✓ 版本文件上传成功")
                    self.log(f"访问地址: {self.domain}/version.json")
                else:
                    self.log(f"✗ 版本文件上传失败: {result}")
                    
            except Exception as e:
                self.log(f"生成版本文件时出错: {str(e)}")
                
        threading.Thread(target=generate_thread, daemon=True).start()
        
    def run(self):
        """运行应用"""
        self.root.mainloop()

if __name__ == "__main__":
    app = UploadTool()
    app.run()
