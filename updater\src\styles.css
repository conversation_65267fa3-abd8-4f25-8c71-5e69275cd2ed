* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
}

.updater-container {
  max-width: 450px;
  margin: 0 auto;
  padding: 15px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  text-align: center;
  margin-bottom: 20px;
  color: white;
}

.header h1 {
  font-size: 22px;
  margin-bottom: 6px;
  font-weight: 600;
}

.header p {
  font-size: 13px;
  opacity: 0.9;
}

.content {
  flex: 1;
  background: white;
  border-radius: 10px;
  padding: 18px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
}

.loading-container {
  text-align: center;
  padding: 40px 0;
  color: #666;
}

.error-container {
  text-align: center;
  padding: 40px 0;
  color: #e74c3c;
}

.error-container p {
  margin-bottom: 20px;
  font-size: 16px;
}

.status-list {
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  margin-bottom: 12px;
  background: #fafbfc;
  transition: all 0.2s ease;
}

.status-item:hover {
  background: #f1f3f4;
  border-color: #d0d7de;
}

.status-info {
  flex: 1;
}

.status-name {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.status-version {
  font-size: 14px;
  color: #656d76;
  margin-bottom: 4px;
}

.status-description {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.status-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
}

.status-size {
  font-size: 12px;
  color: #999;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-latest {
  background: #d1f7d1;
  color: #0969da;
}

.status-update {
  background: #fff3cd;
  color: #856404;
}

.progress-container {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.progress-item {
  margin-bottom: 15px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
}

.progress-status {
  color: #666;
  font-size: 12px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e1e5e9;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.progress-error {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%) !important;
}

.actions {
  text-align: center;
  margin-bottom: 20px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
}

.btn-success:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.last-check {
  text-align: center;
  margin-top: 20px;
  font-size: 12px;
  color: #999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
