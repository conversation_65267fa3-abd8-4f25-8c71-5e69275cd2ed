export interface ComponentInfo {
  name: string
  version: string
  local_path: string
  remote_filename: string
  checksum: string
  checksum_type: string
  size: number
  description: string
}

export interface UpdateManifest {
  version: string
  last_updated: string
  download_base_url: string
  components: Record<string, ComponentInfo>
}

export interface LocalVersion {
  version: string
  last_checked: string
  components: Record<string, {
    version: string
    checksum: string
    last_updated: string
  }>
}

export interface ComponentStatus {
  id: string
  name: string
  currentVersion: string
  latestVersion: string
  hasUpdate: boolean
  size: number
  description: string
}

export interface UpdateProgress {
  componentId: string
  componentName: string
  progress: number
  status: 'downloading' | 'extracting' | 'completed' | 'error'
  message: string
}
