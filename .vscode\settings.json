{"[jsonc]": {"editor.suggest.insertMode": "replace", "editor.codeActionsOnSave": {"source.organizeImports": "never"}}, "[markdown]": {"editor.unicodeHighlight.ambiguousCharacters": false, "editor.unicodeHighlight.invisibleCharacters": false, "diffEditor.ignoreTrimWhitespace": false, "editor.wordWrap": "on", "editor.codeActionsOnSave": {"source.organizeImports": "never"}}, "[json]": {"editor.suggest.insertMode": "replace", "editor.codeActionsOnSave": {"source.organizeImports": "never"}}}