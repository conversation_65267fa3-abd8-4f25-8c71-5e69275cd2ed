#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def check_database():
    db_file = "tft_data.db"
    
    if not os.path.exists(db_file):
        print(f"数据库文件不存在: {db_file}")
        return
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 检查所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print("数据库中的所有表:")
        for table in tables:
            print(f"  - {table[0]}")
        
        # 检查powerups相关的表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%power%';")
        power_tables = cursor.fetchall()
        print(f"\nPowerUps相关表: {[t[0] for t in power_tables]}")
        
        # 如果powerups表存在，检查其结构
        if any('powerups' in t[0] for t in tables):
            print("\npowerups表结构:")
            cursor.execute("PRAGMA table_info(powerups);")
            columns = cursor.fetchall()
            for col in columns:
                print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else ''} {'PRIMARY KEY' if col[5] else ''}")
            
            # 检查数据数量
            cursor.execute("SELECT COUNT(*) FROM powerups;")
            count = cursor.fetchone()[0]
            print(f"\npowerups表中的记录数: {count}")
            
            if count > 0:
                cursor.execute("SELECT powerup_name FROM powerups LIMIT 5;")
                samples = cursor.fetchall()
                print("示例数据:")
                for sample in samples:
                    print(f"  - {sample[0]}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库时出错: {e}")

if __name__ == "__main__":
    check_database()
