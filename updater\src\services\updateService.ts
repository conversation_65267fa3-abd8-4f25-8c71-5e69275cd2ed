import { invoke } from '@tauri-apps/api/core'
import type { UpdateManifest, LocalVersion, ComponentStatus, UpdateProgress } from '@/types'

class UpdateService {
  private readonly configUrl = 'http://updater.yuxianglei.com/yimiaojue_config.json'

  async getLocalVersion(): Promise<LocalVersion> {
    try {
      return await invoke('get_local_version')
    } catch (error) {
      console.warn('无法读取本地版本文件，使用默认版本')
      return this.getDefaultLocalVersion()
    }
  }

  async getRemoteManifest(): Promise<UpdateManifest> {
    try {
      // 先获取ECS配置服务器的配置
      const configResponse = await invoke('get_config_from_server', { url: this.configUrl })

      // 从配置中获取版本信息URL和下载基地址
      const versionUrl = configResponse.release?.version_url
      const downloadBaseUrl = configResponse.release?.download_base_url

      if (!versionUrl || !downloadBaseUrl) {
        throw new Error('配置中未找到版本信息URL或下载基地址')
      }

      // 获取实际的版本信息
      const manifest = await invoke('get_remote_manifest', { url: versionUrl })

      // 确保下载基地址正确设置
      manifest.download_base_url = downloadBaseUrl

      return manifest
    } catch (error) {
      console.error('获取远程配置失败:', error)
      throw new Error('无法获取更新信息，请检查网络连接')
    }
  }

  async checkUpdates(): Promise<ComponentStatus[]> {
    const [localVersion, remoteManifest] = await Promise.all([
      this.getLocalVersion(),
      this.getRemoteManifest()
    ])

    const statuses: ComponentStatus[] = []

    for (const [componentId, remoteComponent] of Object.entries(remoteManifest.components)) {
      const localComponent = localVersion.components[componentId]
      const currentVersion = localComponent?.version || '0.0.0'
      const hasUpdate = !localComponent || localComponent.version !== remoteComponent.version

      statuses.push({
        id: componentId,
        name: remoteComponent.name,
        currentVersion,
        latestVersion: remoteComponent.version,
        hasUpdate,
        size: remoteComponent.size,
        description: remoteComponent.description
      })
    }

    return statuses
  }

  async downloadComponent(
    componentId: string,
    manifest: UpdateManifest,
    onProgress: (progress: UpdateProgress) => void
  ): Promise<void> {
    const component = manifest.components[componentId]
    if (!component) {
      throw new Error(`组件 ${componentId} 不存在`)
    }

    const downloadUrl = `${manifest.download_base_url}${component.remote_filename}`
    
    onProgress({
      componentId,
      componentName: component.name,
      progress: 0,
      status: 'downloading',
      message: '开始下载...'
    })

    try {
      // 调用Tauri后端下载和处理文件
      await invoke('download_and_update_component', {
        componentId,
        downloadUrl,
        expectedChecksum: component.checksum,
        localPath: component.local_path
      })

      onProgress({
        componentId,
        componentName: component.name,
        progress: 100,
        status: 'completed',
        message: '更新完成'
      })

    } catch (error) {
      onProgress({
        componentId,
        componentName: component.name,
        progress: 0,
        status: 'error',
        message: `更新失败: ${error instanceof Error ? error.message : '未知错误'}`
      })
      throw error
    }
  }

  async updateLocalVersion(componentId: string, newVersion: string, checksum: string): Promise<void> {
    try {
      await invoke('update_local_version', {
        componentId,
        version: newVersion,
        checksum,
        lastUpdated: new Date().toISOString()
      })
    } catch (error) {
      console.error('更新本地版本信息失败:', error)
      throw error
    }
  }

  private getDefaultLocalVersion(): LocalVersion {
    return {
      version: '1.0.0',
      last_checked: new Date().toISOString(),
      components: {}
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  async launchMainApplication(): Promise<void> {
    try {
      await invoke('launch_main_application')
    } catch (error) {
      console.error('启动主程序失败:', error)
      throw new Error('无法启动主程序')
    }
  }
}

export const updateService = new UpdateService()
