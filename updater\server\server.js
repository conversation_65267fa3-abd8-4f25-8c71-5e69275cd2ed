import express from 'express'
import cors from 'cors'
import multer from 'multer'
import fs from 'fs/promises'
import path from 'path'
import crypto from 'crypto'
import yauzl from 'yauzl'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const app = express()
const port = 3001

// 中间件
app.use(cors())
app.use(express.json())

// 配置multer用于文件上传
const upload = multer({ 
  dest: path.join(__dirname, 'temp'),
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB限制
  }
})

// 项目根目录路径
const projectRoot = path.resolve(__dirname, '../../..')

// 计算文件MD5
async function calculateMD5(filePath) {
  const fileBuffer = await fs.readFile(filePath)
  const hashSum = crypto.createHash('md5')
  hashSum.update(fileBuffer)
  return hashSum.digest('hex')
}

// 解压ZIP文件
async function extractZip(zipPath, extractPath) {
  return new Promise((resolve, reject) => {
    yauzl.open(zipPath, { lazyEntries: true }, (err, zipfile) => {
      if (err) return reject(err)
      
      zipfile.readEntry()
      zipfile.on('entry', (entry) => {
        if (/\/$/.test(entry.fileName)) {
          // 目录
          zipfile.readEntry()
        } else {
          // 文件
          zipfile.openReadStream(entry, (err, readStream) => {
            if (err) return reject(err)
            
            const filePath = path.join(extractPath, entry.fileName)
            const dirPath = path.dirname(filePath)
            
            // 确保目录存在
            fs.mkdir(dirPath, { recursive: true }).then(() => {
              const writeStream = fs.createWriteStream(filePath)
              readStream.pipe(writeStream)
              writeStream.on('close', () => {
                zipfile.readEntry()
              })
            }).catch(reject)
          })
        }
      })
      
      zipfile.on('end', () => {
        resolve()
      })
      
      zipfile.on('error', reject)
    })
  })
}

// API: 处理组件更新
app.post('/api/update-component', upload.single('file'), async (req, res) => {
  try {
    const { componentId, checksum } = req.body
    const uploadedFile = req.file
    
    if (!uploadedFile) {
      return res.status(400).json({ error: '没有上传文件' })
    }
    
    console.log(`处理组件更新: ${componentId}`)
    
    // 验证文件校验和
    const fileChecksum = await calculateMD5(uploadedFile.path)
    if (fileChecksum !== checksum) {
      await fs.unlink(uploadedFile.path) // 删除临时文件
      return res.status(400).json({ 
        error: '文件校验失败',
        expected: checksum,
        actual: fileChecksum
      })
    }
    
    // 根据组件类型处理文件
    await processComponent(componentId, uploadedFile.path)
    
    // 删除临时文件
    await fs.unlink(uploadedFile.path)
    
    res.json({ success: true, message: '组件更新成功' })
    
  } catch (error) {
    console.error('更新组件失败:', error)
    res.status(500).json({ error: error.message })
  }
})

// 处理不同类型的组件
async function processComponent(componentId, filePath) {
  const componentConfig = {
    database: {
      targetPath: path.join(projectRoot, 'tft_data.db'),
      isZip: false
    },
    hero_icons: {
      targetPath: path.join(projectRoot, '英雄图标'),
      isZip: true
    },
    item_icons: {
      targetPath: path.join(projectRoot, '装备图标'),
      isZip: true
    },
    trait_icons: {
      targetPath: path.join(projectRoot, '羁绊图标'),
      isZip: true
    },
    hex_icons: {
      targetPath: path.join(projectRoot, '海克斯数据', '海克斯图标'),
      isZip: true
    }
  }
  
  const config = componentConfig[componentId]
  if (!config) {
    throw new Error(`未知的组件类型: ${componentId}`)
  }
  
  if (config.isZip) {
    // 处理ZIP文件
    console.log(`解压 ${componentId} 到 ${config.targetPath}`)
    
    // 备份现有目录
    const backupPath = `${config.targetPath}.backup.${Date.now()}`
    try {
      await fs.rename(config.targetPath, backupPath)
    } catch (error) {
      // 目录可能不存在，忽略错误
    }
    
    try {
      // 创建目标目录
      await fs.mkdir(config.targetPath, { recursive: true })
      
      // 解压文件
      await extractZip(filePath, config.targetPath)
      
      // 删除备份
      try {
        await fs.rm(backupPath, { recursive: true, force: true })
      } catch (error) {
        // 忽略删除备份的错误
      }
      
    } catch (error) {
      // 恢复备份
      try {
        await fs.rm(config.targetPath, { recursive: true, force: true })
        await fs.rename(backupPath, config.targetPath)
      } catch (restoreError) {
        console.error('恢复备份失败:', restoreError)
      }
      throw error
    }
    
  } else {
    // 处理单个文件（如数据库）
    console.log(`替换 ${componentId} 文件: ${config.targetPath}`)
    
    // 备份现有文件
    const backupPath = `${config.targetPath}.backup.${Date.now()}`
    try {
      await fs.copyFile(config.targetPath, backupPath)
    } catch (error) {
      // 文件可能不存在，忽略错误
    }
    
    try {
      // 复制新文件
      await fs.copyFile(filePath, config.targetPath)
      
      // 删除备份
      try {
        await fs.unlink(backupPath)
      } catch (error) {
        // 忽略删除备份的错误
      }
      
    } catch (error) {
      // 恢复备份
      try {
        await fs.copyFile(backupPath, config.targetPath)
      } catch (restoreError) {
        console.error('恢复备份失败:', restoreError)
      }
      throw error
    }
  }
}

// API: 获取本地版本信息
app.get('/api/local-version', async (req, res) => {
  try {
    const configPath = path.join(projectRoot, 'config', 'local-version.json')
    const data = await fs.readFile(configPath, 'utf-8')
    res.json(JSON.parse(data))
  } catch (error) {
    // 如果文件不存在，返回默认版本
    res.json({
      version: '1.0.0',
      last_checked: new Date().toISOString(),
      components: {}
    })
  }
})

// API: 更新本地版本信息
app.post('/api/local-version', async (req, res) => {
  try {
    const configPath = path.join(projectRoot, 'config', 'local-version.json')
    await fs.mkdir(path.dirname(configPath), { recursive: true })
    await fs.writeFile(configPath, JSON.stringify(req.body, null, 2))
    res.json({ success: true })
  } catch (error) {
    console.error('保存本地版本信息失败:', error)
    res.status(500).json({ error: error.message })
  }
})

// 启动服务器
app.listen(port, () => {
  console.log(`TFT更新器服务器运行在 http://localhost:${port}`)
  console.log(`项目根目录: ${projectRoot}`)
})
