<template>
  <div class="equipment-recommend-list">


    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>加载装备数据中...</p>
      </div>

      <!-- 无数据状态 -->
      <div v-else-if="filteredData.length === 0" class="no-data-state">
        <div class="no-data-icon">📦</div>
        <h3>{{ equipmentData.length === 0 ? '暂无数据' : '未找到匹配的装备组合' }}</h3>
        <p>{{ equipmentData.length === 0 ? '该英雄暂无装备推荐数据' : '请尝试其他搜索条件' }}</p>
      </div>

      <!-- 装备表格 -->
      <div v-else class="equipment-table">
        <!-- 表头 -->
        <div class="table-header">
          <div class="header-cell equipment-col">装备</div>
          <div class="header-cell sortable" @click="handleSort('play_rate')">
            出场率
            <span v-if="sortColumn === 'play_rate'" class="sort-indicator">
              {{ sortOrder === 'desc' ? '▼' : '▲' }}
            </span>
          </div>
          <div class="header-cell sortable" @click="handleSort('avg_place')">
            平均排名
            <span v-if="sortColumn === 'avg_place'" class="sort-indicator">
              {{ sortOrder === 'desc' ? '▼' : '▲' }}
            </span>
          </div>
          <div class="header-cell sortable" @click="handleSort('top4_rate')">
            前四率
            <span v-if="sortColumn === 'top4_rate'" class="sort-indicator">
              {{ sortOrder === 'desc' ? '▼' : '▲' }}
            </span>
          </div>
          <div class="header-cell sortable" @click="handleSort('top1_rate')">
            登顶率
            <span v-if="sortColumn === 'top1_rate'" class="sort-indicator">
              {{ sortOrder === 'desc' ? '▼' : '▲' }}
            </span>
          </div>
          <div v-if="itemCount < 3" class="header-cell actions-col"></div>
        </div>

        <!-- 装备列表 -->
        <div class="equipment-list">
        <div
          v-for="(item, index) in filteredData"
          :key="index"
          class="equipment-row"
          @click="handleRowClick(item)"
        >
          <!-- 装备图标列 -->
          <div class="equipment-col">
            <div class="equipment-icons">
              <ItemIcon
                v-for="(itemName, idx) in getItemNames(item)"
                :key="idx"
                :item-name="itemName"
                :icon-path="getItemIconPath(item, idx)"
                :size="32"
                @click="handleItemClick(itemName)"
              />
            </div>
            <div v-if="itemCount === 1 && getItemNames(item).length > 0" class="equipment-name">
              {{ getItemNames(item)[0] }}
            </div>
          </div>

          <!-- 统计数据列 -->
          <div class="stat-col">{{ formatPlayRate(item.play_rate) }}</div>
          <div class="stat-col">{{ formatAvgPlace(item.avg_place) }}</div>
          <div class="stat-col">{{ formatTop4Rate(item.top4_rate) }}</div>
          <div class="stat-col">{{ formatTop1Rate(item.top1_rate) }}</div>

          <!-- 操作列 -->
          <div v-if="itemCount < 3" class="actions-col">
            <button
              class="magnify-button"
              :title="`查找包含这些装备的${itemCount + 1}件套`"
              @click.stop="handleMagnifyClick(item)"
            >
              🔍
            </button>
          </div>
        </div>

        <!-- 加载提示 -->
        <div v-if="showLoadingHint && itemCount === 1 && !loading" class="loading-hint">
          <div class="hint-content">
            <span class="hint-icon">⚡</span>
            <span class="hint-text">更多装备数据正在后台加载中...</span>
          </div>
        </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import ItemIcon from '@/components/common/ItemIcon.vue'

// Props
interface Props {
  itemCount: number
  equipmentData: any[]
  loading: boolean
  searchQuery?: string
  showLoadingHint?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  searchQuery: '',
  showLoadingHint: false
})

// Emits
const emit = defineEmits<{
  itemSelected: [itemName: string]
  magnifyRequested: [currentItems: string[]]
}>()

// 响应式数据
const sortColumn = ref('play_rate')
const sortOrder = ref<'asc' | 'desc'>('desc')

// 计算属性
const filteredData = computed(() => {
  let data = [...props.equipmentData]

  // 搜索筛选
  if (props.searchQuery && props.searchQuery.trim()) {
    const query = props.searchQuery.toLowerCase().trim()
    const searchTerms = query.split(/\s+/)

    data = data.filter(item => {
      const itemNames = getItemNames(item).join(' ').toLowerCase()
      return searchTerms.every(term => itemNames.includes(term))
    })
  }

  // 排序
  data.sort((a, b) => {
    const aVal = a[sortColumn.value] ?? (sortOrder.value === 'desc' ? -Infinity : Infinity)
    const bVal = b[sortColumn.value] ?? (sortOrder.value === 'desc' ? -Infinity : Infinity)
    
    if (sortOrder.value === 'desc') {
      return bVal - aVal
    } else {
      return aVal - bVal
    }
  })

  return data
})

// 方法
const getItemNames = (item: any): string[] => {
  const itemNames = item.item_names || ''
  return itemNames.split('|').filter((name: string) => name.trim())
}

const getItemIconPath = (item: any, index: number): string | null => {
  if (item.icon_paths && Array.isArray(item.icon_paths) && item.icon_paths[index]) {
    return item.icon_paths[index]
  }
  return null
}

const formatPlayRate = (rate: number | null | undefined) => {
  if (rate == null) return '--'
  return `${rate.toFixed(1)}%`
}

const formatAvgPlace = (place: number | null | undefined) => {
  if (place == null) return '--'
  return place.toFixed(2)
}

const formatTop4Rate = (rate: number | null | undefined) => {
  if (rate == null) return '--'
  return `${rate.toFixed(1)}%`
}

const formatTop1Rate = (rate: number | null | undefined) => {
  if (rate == null) return '--'
  return `${rate.toFixed(1)}%`
}

const handleSort = (column: string) => {
  if (sortColumn.value === column) {
    sortOrder.value = sortOrder.value === 'desc' ? 'asc' : 'desc'
  } else {
    sortColumn.value = column
    // 设置各列默认排序
    if (column === 'avg_place') {
      sortOrder.value = 'asc' // 平均排名越小越好
    } else {
      sortOrder.value = 'desc' // 其他指标越大越好
    }
  }
}

const handleRowClick = (item: any) => {
  // 行点击处理
  console.log('点击装备行:', item)
}

const handleItemClick = (itemName: string) => {
  emit('itemSelected', itemName)
}

const handleMagnifyClick = (item: any) => {
  const itemNames = getItemNames(item)
  emit('magnifyRequested', itemNames)
}

// 监听数据变化，重置排序
watch(() => props.equipmentData, () => {
  sortColumn.value = 'play_rate'
  sortOrder.value = 'desc'
})
</script>

<style scoped>
.equipment-recommend-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  min-height: 0;
}

/* 搜索区域 */
.search-section {
  flex-shrink: 0;
  padding: 0 1rem;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 0.5rem;
  gap: 0.5rem;
  max-width: 300px;
}

.search-icon {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
}

.search-input {
  flex: 1;
  background: transparent;
  border: none;
  color: white;
  font-size: 0.875rem;
  outline: none;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* 装备表格 */
.equipment-table {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 表头 */
.table-header {
  display: grid;
  grid-template-columns: 1fr 80px 90px 80px 80px 40px;
  gap: 0.5rem;
  padding: 1rem 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.header-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.header-cell.equipment-col {
  justify-content: flex-start;
}

.header-cell.actions-col {
  width: 40px;
}

.header-cell.sortable {
  cursor: pointer;
  transition: color 0.2s ease;
  user-select: none;
}

.header-cell.sortable:hover {
  color: white;
}

.sort-indicator {
  margin-left: 0.25rem;
  font-size: 0.75rem;
}

/* 内容区域 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 0.3rem 0.5rem 0.3rem;
  min-height: 0;
}

/* 装备列表滚动条样式 */
.equipment-list::-webkit-scrollbar {
  width: 6px;
}

.equipment-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  margin: 4px 0;
}

.equipment-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.4);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.equipment-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.6);
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: rgba(255, 255, 255, 0.7);
  gap: 1rem;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 无数据状态 */
.no-data-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: rgba(255, 255, 255, 0.7);
  gap: 0.75rem;
  text-align: center;
}

.no-data-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.no-data-state h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.no-data-state p {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.5);
  margin: 0;
}

/* 装备列表 */
.equipment-list {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: calc(100vh - 400px);
  min-height: 200px;
  gap: 0.15rem;
}

.equipment-row {
  display: grid;
  grid-template-columns: 1fr 80px 90px 80px 80px 40px;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.2s ease;
  align-items: center;
  min-height: 60px;
}

.equipment-row:hover {
  background: rgba(255, 255, 255, 0.08);
}

.equipment-row:last-child {
  border-bottom: none;
}

.equipment-col {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: flex-start;
}

.equipment-icons {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.equipment-name {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.stat-col {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  text-align: center;
}

.actions-col {
  display: flex;
  align-items: center;
  justify-content: center;
}

.magnify-button {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
}

.magnify-button:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: scale(1.1);
}

/* 加载提示样式 */
.loading-hint {
  padding: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  background: rgba(255, 255, 255, 0.02);
}

.hint-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  font-weight: 500;
}

.hint-icon {
  font-size: 1rem;
  animation: pulse 2s infinite;
}

.hint-text {
  font-style: italic;
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-header,
  .equipment-row {
    grid-template-columns: 1fr 60px 70px 60px 60px 32px;
    gap: 0.25rem;
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  .equipment-icons {
    gap: 0.125rem;
  }

  .equipment-name {
    font-size: 0.75rem;
  }

  .magnify-button {
    width: 24px;
    height: 24px;
    font-size: 0.75rem;
  }

  .hint-content {
    font-size: 0.75rem;
  }
}
</style>
