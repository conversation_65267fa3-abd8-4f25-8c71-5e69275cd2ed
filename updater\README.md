# TFT助手更新器

一个简洁的Vue.js更新器，用于管理TFT助手的数据库和图标资源更新。

## 功能特性

- 🔍 **自动检查更新** - 检查数据库和图标文件的最新版本
- 📦 **一键更新** - 简单点击即可更新所有组件
- 📊 **进度显示** - 实时显示下载和安装进度
- 🔒 **文件校验** - MD5校验确保文件完整性
- 🔄 **自动备份** - 更新前自动备份现有文件

## 管理的组件

- **数据库** (`tft_data.db`) - 游戏数据库文件
- **英雄图标** (`英雄图标/`) - 英雄头像图标
- **装备图标** (`装备图标/`) - 装备图标文件
- **羁绊图标** (`羁绊图标/`) - 羁绊图标文件
- **海克斯图标** (`海克斯数据/海克斯图标/`) - 海克斯强化图标

## 快速开始

### 1. 安装依赖

```bash
# 安装前端依赖
npm install

# 安装后端依赖
cd server
npm install
cd ..
```

### 2. 启动更新器

**方式一：使用批处理文件（推荐）**
```bash
start-updater.bat
```

**方式二：手动启动**
```bash
# 启动后端服务器
cd server
npm start

# 新开终端，启动前端
npm run dev
```

### 3. 使用更新器

1. 打开浏览器访问 `http://localhost:5174`
2. 点击"检查更新"查看可用更新
3. 点击"一键更新"应用所有更新

## 配置文件

### update-manifest.json
远程更新配置文件，定义了各组件的版本信息和下载地址。

### local-version.json
本地版本记录文件，记录当前安装的各组件版本。

## 技术架构

- **前端**: Vue 3 + TypeScript + Vite
- **后端**: Node.js + Express
- **文件处理**: yauzl (ZIP解压)
- **校验**: MD5哈希验证

## 更新流程

1. **检查更新** - 对比本地版本与远程版本
2. **下载文件** - 从七牛云下载更新文件
3. **校验文件** - MD5校验确保文件完整性
4. **备份现有** - 自动备份当前文件
5. **应用更新** - 解压/替换文件
6. **更新记录** - 更新本地版本信息

## 注意事项

- 确保有足够的磁盘空间进行备份和更新
- 更新过程中请勿关闭程序
- 如果更新失败，程序会自动恢复备份
- 建议在更新前关闭主程序

## 故障排除

### 无法连接到服务器
- 检查后端服务器是否正常启动
- 确认端口3001没有被占用

### 下载失败
- 检查网络连接
- 确认七牛云服务可访问

### 文件校验失败
- 重新下载文件
- 检查网络稳定性

## 开发说明

### 项目结构
```
updater/
├── src/                 # Vue前端源码
├── server/              # Node.js后端
├── config/              # 配置文件
└── dist/                # 构建输出
```

### 构建生产版本
```bash
npm run build
```

### API接口

- `GET /api/local-version` - 获取本地版本信息
- `POST /api/local-version` - 更新本地版本信息
- `POST /api/update-component` - 处理组件更新
