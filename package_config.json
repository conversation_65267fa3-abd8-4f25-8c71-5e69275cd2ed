{"package_info": {"name": "弈秒决TFT助手", "version": "1.0.0", "description": "云顶之弈游戏助手完整版"}, "directory_structure": {"root": {"启动弈秒决.bat": "主启动器", "弈秒决更新器.exe": "更新器程序", "README.txt": "使用说明", "version_info.json": "版本信息"}, "main_app": {"path": "弈秒决/", "description": "主程序目录（PyInstaller onedir输出）", "contains": ["弈秒决.exe", "_internal/", "各种依赖文件"]}, "external_data": {"tft_data.db": "游戏数据库文件", "英雄图标/": "英雄头像图标目录", "装备图标/": "装备图标目录", "羁绊图标/": "羁绊图标目录", "海克斯数据/": "海克斯强化数据目录", "config/": "配置文件目录"}}, "build_steps": [{"step": 1, "name": "打包Python主程序", "command": "pyinstaller --onedir --windowed", "description": "使用PyInstaller将Python代码打包为onedir格式"}, {"step": 2, "name": "构建Tauri更新器", "command": "npm run tauri build", "description": "构建Tauri桌面更新器应用"}, {"step": 3, "name": "复制外置数据", "description": "复制数据库和图标文件到发布目录"}, {"step": 4, "name": "创建启动器", "description": "创建批处理启动脚本"}, {"step": 5, "name": "打包发布", "description": "创建最终的ZIP发布包"}], "deployment_structure": {"description": "部署后的目录结构，确保更新器能正确找到文件", "paths": {"updater_exe": "./弈秒决更新器.exe", "main_app_dir": "./弈秒决/", "main_app_exe": "./弈秒决/弈秒决.exe", "data_files": {"database": "./tft_data.db", "hero_icons": "./英雄图标/", "item_icons": "./装备图标/", "trait_icons": "./羁绊图标/", "hex_data": "./海克斯数据/"}, "config_dir": "./config/"}}, "update_logic": {"description": "更新器的查找逻辑", "search_order": ["更新器同级目录查找主程序", "更新器上级目录查找主程序", "查找弈秒决/目录", "查找YimiaoJue/目录", "查找main_app/目录"], "data_placement": "外置数据文件放在主程序目录或其上级目录"}}