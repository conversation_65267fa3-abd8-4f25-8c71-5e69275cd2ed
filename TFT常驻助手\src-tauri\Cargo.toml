[package]
name = "tft-assistant"
version = "0.0.0"
description = "TFT Assistant - Teamfight Tactics Helper"
authors = ["you"]
license = ""
repository = ""
default-run = "tft-assistant"
edition = "2021"
rust-version = "1.60"





# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
tauri = { version = "2", features = ["wry"] }
tauri-plugin-shell = "2"
tokio = { version = "1.0", features = ["full"] }
rusqlite = { version = "0.29", features = ["bundled"] }
thiserror = "1.0"
base64 = "0.21"
lazy_static = "1.4"
reqwest = { version = "0.11", features = ["json"] }

[features]
# this feature is used for production builds or when `devPath` points to the filesystem and the built-in dev server is disabled.
# If you use cargo directly instead of tauri's cli you can use this feature flag to switch between tauri's `dev` and `build` modes.
# DO NOT REMOVE!!
custom-protocol = [ "tauri/custom-protocol" ]

[profile.release]
# 启用最高级别的优化
opt-level = 3
# 启用链接时优化
lto = true
# 减少代码大小
codegen-units = 1
# 启用panic时立即终止，减少二进制大小
panic = "abort"
# 去除调试信息
debug = false
# 启用更激进的优化
strip = true
