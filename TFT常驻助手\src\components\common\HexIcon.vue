<template>
  <div 
    class="hex-icon-container"
    :style="{ width: `${size}px`, height: `${size}px` }"
    @click="handleClick"
  >
    <!-- 图标图片 -->
    <img 
      v-if="iconSrc && !imageError"
      :src="iconSrc"
      :alt="hexName"
      class="hex-icon-image"
      @load="onImageLoad"
      @error="onImageError"
    />
    
    <!-- 加载状态 -->
    <div
      v-else-if="isLoading"
      class="hex-icon-loading"
    >
      <div class="loading-shimmer"></div>
    </div>

    <!-- 占位符（延迟显示） -->
    <div
      v-else-if="showPlaceholder"
      class="hex-icon-placeholder"
      :style="{ fontSize: `${size * 0.3}px` }"
    >
      {{ placeholder }}
    </div>

    <!-- 空状态 -->
    <div
      v-else
      class="hex-icon-empty"
    >
      <div class="empty-icon">🔮</div>
    </div>
    
    <!-- 评级徽章 -->
    <div 
      v-if="showTierBadge && tier"
      class="tier-badge"
      :class="getTierBadgeClass(tier)"
    >
      {{ tier }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { getSmartIconPath } from '@/utils/iconUtils'

// Props
interface Props {
  hexName: string
  iconPath?: string | null
  tier?: string
  size?: number
  showTierBadge?: boolean
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 60,
  showTierBadge: true,
  clickable: true
})

// Emits
const emit = defineEmits<{
  click: [hexName: string]
}>()

// 响应式数据
const iconSrc = ref<string | null>(null)
const imageError = ref(false)
const isLoading = ref(false)
const showPlaceholder = ref(true) // 控制占位符显示

// 全局图标缓存
const iconCache = new Map<string, string | null>()

// 延迟显示占位符，避免闪烁
const placeholderDelay = ref<number | null>(null)

// 计算属性
const placeholder = computed(() => {
  if (!props.hexName) return '?'
  return props.hexName.charAt(0).toUpperCase()
})

// 评级徽章样式类
const getTierBadgeClass = (tier: string) => {
  const tierClasses = {
    'S': 'tier-s-badge',
    'A': 'tier-a-badge',
    'B': 'tier-b-badge',
    'C': 'tier-c-badge',
    'D': 'tier-d-badge'
  }
  return tierClasses[tier as keyof typeof tierClasses] || 'tier-default-badge'
}

// 加载图标
const loadIcon = async () => {
  // 清除之前的延迟定时器
  if (placeholderDelay.value) {
    clearTimeout(placeholderDelay.value)
    placeholderDelay.value = null
  }

  if (!props.iconPath) {
    iconSrc.value = null
    showPlaceholder.value = true
    return
  }

  // 检查缓存
  const cacheKey = props.iconPath
  if (iconCache.has(cacheKey)) {
    const cachedResult = iconCache.get(cacheKey)
    iconSrc.value = cachedResult || null
    imageError.value = cachedResult === null
    showPlaceholder.value = cachedResult === null
    isLoading.value = false
    return
  }

  // 开始加载状态
  isLoading.value = true
  showPlaceholder.value = false
  imageError.value = false

  // 设置延迟显示占位符（如果加载时间过长）
  placeholderDelay.value = setTimeout(() => {
    if (isLoading.value && !iconSrc.value) {
      showPlaceholder.value = true
      isLoading.value = false
    }
  }, 800) // 800ms后显示占位符

  try {
    const smartPath = await getSmartIconPath(props.iconPath)

    // 缓存结果
    iconCache.set(cacheKey, smartPath)
    iconSrc.value = smartPath

    if (smartPath) {
      showPlaceholder.value = false
    } else {
      console.warn(`未找到海克斯 ${props.hexName} 的图标: ${props.iconPath}`)
      showPlaceholder.value = true
    }
  } catch (error) {
    console.error(`加载海克斯 ${props.hexName} 图标失败:`, error)
    iconCache.set(cacheKey, null)
    iconSrc.value = null
    showPlaceholder.value = true
  } finally {
    isLoading.value = false
    if (placeholderDelay.value) {
      clearTimeout(placeholderDelay.value)
      placeholderDelay.value = null
    }
  }
}

// 图片加载成功
const onImageLoad = () => {
  imageError.value = false
  showPlaceholder.value = false
  isLoading.value = false
}

// 图片加载失败
const onImageError = () => {
  console.warn(`海克斯 ${props.hexName} 图标加载失败: ${iconSrc.value}`)
  imageError.value = true
  showPlaceholder.value = true
  isLoading.value = false
}

// 点击处理
const handleClick = () => {
  if (props.clickable) {
    emit('click', props.hexName)
  }
}

// 监听iconPath变化
watch(() => props.iconPath, loadIcon, { immediate: true })

// 组件挂载时加载图标
onMounted(() => {
  loadIcon()
})
</script>

<style scoped>
.hex-icon-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.hex-icon-container:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.hex-icon-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}



/* === 加载状态 === */
.hex-icon-loading {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
}

.loading-shimmer {
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  animation: shimmer 1.5s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* === 占位符（优化后） === */
.hex-icon-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.08);
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

/* === 空状态 === */
.hex-icon-empty {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  opacity: 0.6;
}

.empty-icon {
  font-size: 24px;
  opacity: 0.5;
}

.tier-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 700;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.tier-s-badge { background: linear-gradient(135deg, #ff6b9d, #ff4757); }
.tier-a-badge { background: linear-gradient(135deg, #ffa726, #ff8f00); }
.tier-b-badge { background: linear-gradient(135deg, #ffeb3b, #ffc107); }
.tier-c-badge { background: linear-gradient(135deg, #cddc39, #8bc34a); }
.tier-d-badge { background: linear-gradient(135deg, #4caf50, #2e7d32); }
.tier-default-badge { background: linear-gradient(135deg, #666666, #444444); }
</style>