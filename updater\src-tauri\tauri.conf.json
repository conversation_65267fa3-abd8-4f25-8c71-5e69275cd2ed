{"$schema": "https://schema.tauri.app/config/2", "productName": "updater", "version": "0.1.0", "identifier": "com.tft.updater", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"withGlobalTauri": true, "windows": [{"title": "updater", "width": 800, "height": 600}], "security": {"csp": null}}, "bundle": {"active": false, "targets": [], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}