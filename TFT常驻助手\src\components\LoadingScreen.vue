<template>
  <div class="loading-screen" :class="{ 'fade-out': !visible }">
    <div class="loading-content">
      <!-- 应用图标/Logo -->
      <div class="app-logo">
        <div class="logo-icon">弈</div>
      </div>
      
      <!-- 应用名称 -->
      <h1 class="app-name">弈秒决</h1>
      <p class="app-subtitle">云顶之弈助手</p>
      
      <!-- 加载动画 -->
      <div class="loading-animation">
        <div class="loading-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
      
      <!-- 加载状态文字 -->
      <p class="loading-text">{{ loadingText }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface Props {
  visible: boolean
}

const props = defineProps<Props>()

const loadingText = ref('正在启动应用...')

// 优化：更快的加载状态变化
onMounted(() => {
  const loadingSteps = [
    '正在启动应用...',
    '初始化界面...',
    '准备数据...',
    '即将完成...'
  ]

  let currentStep = 0
  const interval = setInterval(() => {
    currentStep++
    if (currentStep < loadingSteps.length) {
      loadingText.value = loadingSteps[currentStep]
    } else {
      clearInterval(interval)
    }
  }, 100) // 优化：更快的状态切换，100ms间隔

  // 清理定时器
  setTimeout(() => {
    clearInterval(interval)
  }, 600) // 与启动画面显示时间同步
})
</script>

<style scoped>
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(45deg, 
    rgb(88, 86, 134) 0%,
    rgb(75, 85, 145) 25%,
    rgb(65, 95, 160) 50%,
    rgb(55, 105, 180) 75%,
    rgb(45, 115, 200) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.5s ease-out;
}

.loading-screen.fade-out {
  opacity: 0;
  pointer-events: none;
}

.loading-content {
  text-align: center;
  color: white;
}

.app-logo {
  margin-bottom: 1.5rem;
}

.logo-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  animation: logoFloat 2s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.app-name {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 1rem 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 2px;
}

.app-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 2rem 0;
  font-weight: 400;
}

.loading-animation {
  margin: 2rem 0 1.5rem 0;
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: dotPulse 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.loading-text {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-weight: 400;
  min-height: 1.2rem;
  transition: all 0.3s ease;
}
</style>
