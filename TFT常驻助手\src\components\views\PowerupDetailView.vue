<template>
  <div class="powerup-detail-view">


    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>正在加载果实详情...</p>
    </div>

    <!-- 果实详情内容 -->
    <div v-else-if="powerupDetail" class="powerup-detail-content">
      
      <!-- 果实基本信息卡片 -->
      <div class="powerup-info-card">
        <!-- 果实名称和评级 -->
        <div class="powerup-header">
          <h1 class="powerup-name">{{ powerupDetail.powerup_name }}</h1>
          <span
            class="tier-badge-large"
            :class="getTierBadgeClass(powerupDetail.tier_rank)"
          >
            {{ powerupDetail.tier_rank }}级
          </span>
        </div>

        <!-- 果实描述 -->
        <div v-if="powerupDetail.description" class="powerup-description-section">
          <div class="description-header">
            <h3 class="section-title">果实效果</h3>
            <span v-if="powerupDetail.hero_count && powerupDetail.hero_count > 0" class="hero-count-badge">
              {{ powerupDetail.hero_count }} 个英雄适用
            </span>
          </div>
          <div class="description-content">
            {{ powerupDetail.description }}
          </div>
        </div>
      </div>

      <!-- 相关英雄评分卡片 -->
      <div class="hero-stats-card">
        <div class="hero-stats-header">
          <h3 class="section-title">相关英雄评分</h3>
          <!-- 搜索框 -->
          <div class="search-input-container">
            <input
              v-model="heroSearchQuery"
              type="text"
              placeholder="搜索英雄..."
              class="hero-search-input"
            />
            <span class="search-icon">🔍</span>
          </div>
        </div>

        <!-- 英雄列表 -->
        <div v-if="filteredHeroStats.length > 0" class="hero-stats-list">
          <div
            v-for="heroStat in filteredHeroStats"
            :key="heroStat.hero_cn_name"
            class="hero-stat-item"
            @click="handleHeroClick(heroStat)"
          >
            <!-- 英雄图标 -->
            <div class="hero-icon-section">
              <HeroIcon
                :hero-name="heroStat.hero_cn_name"
                :icon-path="heroStat.hero_icon_path"
                :cost="heroStat.hero_cost"
                :size="50"
                :show-cost-badge="true"
                :clickable="false"
              />
            </div>

            <!-- 英雄名称 -->
            <div class="hero-name">{{ heroStat.hero_cn_name }}</div>

            <!-- 果实评分 -->
            <div class="powerup-rating-section">
              <span
                class="tier-badge"
                :class="getTierBadgeClass(heroStat.powerup_tier_rank)"
              >
                {{ heroStat.powerup_tier_rank }}
              </span>
            </div>
          </div>
        </div>

        <!-- 无英雄数据提示 -->
        <div v-else class="no-hero-data">
          <div class="no-data-icon">👤</div>
          <p>{{ getNoDataMessage() }}</p>
          <p v-if="!isLoading && heroStats.length === 0" class="no-data-hint">
            该果实可能没有足够的使用数据，或者暂未收录相关英雄评分信息
          </p>
        </div>
      </div>

    </div>

    <!-- 错误状态 -->
    <div v-else class="error-state">
      <div class="error-icon">❌</div>
      <h3>加载失败</h3>
      <p>无法加载果实详情，请稍后重试</p>
      <button @click="loadPowerupDetail" class="retry-button">重新加载</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { invoke } from '@tauri-apps/api/core'
import HeroIcon from '@/components/common/HeroIcon.vue'

// Props
interface Props {
  powerupName: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  back: []
  heroClick: [heroName: string]
}>()

// 果实详情数据接口
interface PowerupDetailData {
  powerup_name: string
  tier_rank: string
  description?: string
  min_stage?: number
  max_stage?: number
  weight?: number
  hero_count?: number
}

// 英雄评分数据接口
interface HeroStatData {
  hero_cn_name: string
  hero_en_name: string
  hero_cost: number
  hero_icon_path: string
  hero_play_rate: number
  hero_avg_place: number
  hero_top4_rate: number
  hero_top1_rate: number
  powerup_tier_rank: string
  powerup_details?: string
  powerup_min_stage?: number
  powerup_max_stage?: number
  powerup_weight?: number
  hero_traits?: string
}

// 响应式数据
const isLoading = ref(false)
const powerupDetail = ref<PowerupDetailData | null>(null)
const heroStats = ref<HeroStatData[]>([])
const heroSearchQuery = ref('')

// 计算属性
const filteredHeroStats = computed(() => {
  let filtered = heroStats.value

  // 搜索过滤
  if (heroSearchQuery.value.trim()) {
    const query = heroSearchQuery.value.toLowerCase()
    filtered = filtered.filter(hero =>
      hero.hero_cn_name.toLowerCase().includes(query) ||
      hero.hero_en_name.toLowerCase().includes(query) ||
      (hero.hero_traits && hero.hero_traits.toLowerCase().includes(query))
    )
  }

  // 按评分排序：S > A > B > C > D，同评分内按出场率排序
  return filtered.sort((a, b) => {
    const tierOrder: Record<string, number> = { 'S': 1, 'A': 2, 'B': 3, 'C': 4, 'D': 5 }
    const aTierOrder = tierOrder[a.powerup_tier_rank] || 6
    const bTierOrder = tierOrder[b.powerup_tier_rank] || 6

    if (aTierOrder !== bTierOrder) {
      return aTierOrder - bTierOrder
    }

    // 同评分内按出场率降序排序
    return (b.hero_play_rate || 0) - (a.hero_play_rate || 0)
  })
})

// 工具方法
const getTierBadgeClass = (tier: string): string => {
  const tierMap: Record<string, string> = {
    'S': 'tier-s',
    'A': 'tier-a', 
    'B': 'tier-b',
    'C': 'tier-c',
    'D': 'tier-d'
  }
  return tierMap[tier] || 'tier-unknown'
}





const getNoDataMessage = (): string => {
  if (isLoading.value) {
    return '正在加载英雄评分数据...'
  }

  if (heroSearchQuery.value.trim()) {
    return '未找到匹配的英雄'
  }

  if (heroStats.value.length === 0) {
    return '暂无相关英雄评分数据'
  }

  return '无数据'
}

// 事件处理
const handleHeroClick = (heroStat: HeroStatData) => {
  console.log('点击英雄:', heroStat.hero_cn_name)
  emit('heroClick', heroStat.hero_cn_name)
}

// 数据加载
const loadPowerupDetail = async () => {
  try {
    isLoading.value = true
    console.log('🔍 开始加载果实详情:', props.powerupName)

    // 并行加载果实详情和英雄评分
    const [detailResults, heroResults] = await Promise.all([
      invoke('get_powerup_detail', { powerupName: props.powerupName }),
      invoke('get_powerup_hero_stats', { powerupName: props.powerupName })
    ])

    console.log('📋 果实详情结果:', detailResults)
    console.log('📊 英雄评分结果:', heroResults)

    // 处理果实详情数据
    if (detailResults && typeof detailResults === 'object' && 'data' in detailResults) {
      if ('error' in detailResults && detailResults.error) {
        console.error('❌ 果实详情查询错误:', detailResults.error)
        powerupDetail.value = null
      } else if (Array.isArray(detailResults.data) && detailResults.data.length > 0) {
        powerupDetail.value = detailResults.data[0]
        console.log('✅ 成功加载果实详情')
      } else {
        console.warn('⚠️ 果实详情数据为空')
        powerupDetail.value = null
      }
    } else if (Array.isArray(detailResults) && detailResults.length > 0) {
      powerupDetail.value = detailResults[0]
      console.log('✅ 成功加载果实详情')
    } else {
      console.warn('⚠️ 果实详情数据格式不正确:', detailResults)
      powerupDetail.value = null
    }

    // 处理英雄评分数据
    if (heroResults && typeof heroResults === 'object' && 'data' in heroResults) {
      if ('error' in heroResults && heroResults.error) {
        console.error('❌ 英雄评分查询错误:', heroResults.error)
        heroStats.value = []
      } else if (Array.isArray(heroResults.data)) {
        heroStats.value = heroResults.data
        console.log(`✅ 成功加载 ${heroStats.value.length} 个英雄评分`)
      } else {
        console.warn('⚠️ 英雄评分数据格式不正确:', heroResults.data)
        heroStats.value = []
      }
    } else if (Array.isArray(heroResults)) {
      heroStats.value = heroResults
      console.log(`✅ 成功加载 ${heroStats.value.length} 个英雄评分`)
    } else {
      console.warn('⚠️ 英雄评分数据格式不正确:', heroResults)
      heroStats.value = []
    }

  } catch (error) {
    console.error('❌ 加载果实详情失败:', error)
    powerupDetail.value = null
    heroStats.value = []
  } finally {
    isLoading.value = false
    console.log('🔍 果实详情加载完成')
  }
}

// 监听果实名称变化
watch(() => props.powerupName, (newPowerupName) => {
  console.log('🔍 果实名称变化:', newPowerupName)
  if (newPowerupName) {
    loadPowerupDetail()
  }
}, { immediate: true })

// 组件挂载时加载数据
onMounted(() => {
  if (props.powerupName) {
    loadPowerupDetail()
  }
})
</script>

<style scoped>
/* === 主容器 === */
.powerup-detail-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0rem 0.75rem 0rem 0.75rem;
  gap: 0.5rem;
  overflow: hidden;
  height: 100%;
  box-sizing: border-box;
}



.back-icon {
  font-size: 1.2rem;
  font-weight: bold;
}

/* === 加载状态 === */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: 1rem;
  color: rgba(255, 255, 255, 0.7);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #60a5fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* === 错误状态 === */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: 1rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

.error-icon {
  font-size: 3rem;
}

.retry-button {
  padding: 0.5rem 1rem;
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  color: #fca5a5;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: rgba(239, 68, 68, 0.3);
  border-color: rgba(239, 68, 68, 0.5);
}

/* === 详情内容 === */
.powerup-detail-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  min-height: 0;
  padding-bottom: 0.5rem; /* 减小底部padding */
}

/* === 果实信息卡片 === */
.powerup-info-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.powerup-header {
  display: flex;
  align-items: center;
  justify-content: center; /* 居中对齐 */
  gap: 1rem; /* 增加间距 */
  flex-wrap: nowrap; /* 强制保持在同一行 */
  min-width: 0; /* 允许子元素收缩 */
}

.powerup-icon-section {
  flex-shrink: 0;
}

.powerup-icon-placeholder {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
}

.powerup-basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.powerup-name {
  font-size: 1.75rem;
  font-weight: bold;
  color: #ffffff;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  text-align: center; /* 文本居中 */
  flex-shrink: 1; /* 允许收缩但不强制占满空间 */
}

.powerup-tier-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.tier-badge-large {
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: bold;
  font-size: 1.1rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  flex-shrink: 0; /* 防止评级标签收缩 */
  white-space: nowrap; /* 防止换行 */
}

.hero-count-badge {
  padding: 0.25rem 0.75rem;
  background: rgba(34, 197, 94, 0.2);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 6px;
  color: #86efac;
  font-size: 0.85rem;
}

/* === 评级颜色 === */
.tier-s {
  background: linear-gradient(135deg, rgba(255, 71, 87, 0.9), rgba(255, 107, 157, 0.9));
  border: 1px solid rgba(255, 71, 87, 0.5);
  color: #ffffff;
}

.tier-a {
  background: linear-gradient(135deg, rgba(255, 143, 0, 0.9), rgba(255, 167, 38, 0.9));
  border: 1px solid rgba(255, 143, 0, 0.5);
  color: #ffffff;
}

.tier-b {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.9), rgba(255, 235, 59, 0.9));
  border: 1px solid rgba(255, 193, 7, 0.5);
  color: #000000;
}

.tier-c {
  background: linear-gradient(135deg, rgba(139, 195, 74, 0.9), rgba(205, 220, 57, 0.9));
  border: 1px solid rgba(139, 195, 74, 0.5);
  color: #000000;
}

.tier-d {
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.9), rgba(76, 175, 80, 0.9));
  border: 1px solid rgba(46, 125, 50, 0.5);
  color: #ffffff;
}

.tier-unknown {
  background: rgba(107, 114, 128, 0.9);
  border: 1px solid rgba(107, 114, 128, 0.5);
  color: #ffffff;
}

/* === 描述部分 === */
.section-title {
  font-size: 1.1rem;
  font-weight: bold;
  color: #ffffff;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.description-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.description-content {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  font-size: 0.9rem;
  background: rgba(0, 0, 0, 0.2);
  padding: 0.75rem;
  border-radius: 8px;
  border-left: 4px solid rgba(59, 130, 246, 0.5);
}

.attributes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.attribute-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.attribute-label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.attribute-value {
  color: #ffffff;
  font-weight: bold;
}

/* === 英雄评分卡片 === */
.hero-stats-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  flex: 1;
  overflow: hidden;
  min-height: 0; /* 允许flex子项收缩 */
  margin-bottom: 0rem; /* 减小底部间距 */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

/* === 英雄评分头部 === */
.hero-stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.hero-count-info {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
}

/* === 英雄搜索 === */
.hero-search-section {
  margin-bottom: 1rem;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input-container {
  position: relative;
  flex: 1;
  max-width: 300px;
}

.hero-search-input {
  width: 100%;
  padding: 0.5rem 2rem 0.5rem 0.75rem;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  font-size: 0.85rem;
  transition: all 0.2s ease;
}

.hero-search-input:focus {
  outline: none;
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(0, 0, 0, 0.4);
}

.hero-search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.search-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.5);
  pointer-events: none;
}

/* === 英雄列表 === */
.hero-stats-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1; /* 使用剩余空间 */
  overflow-y: auto;
  padding-right: 0.5rem;
  padding-bottom: 0.25rem;
  min-height: 0; /* 允许收缩 */
}

.hero-stats-list::-webkit-scrollbar {
  width: 6px;
}

.hero-stats-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.hero-stats-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.hero-stats-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.hero-stat-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.hero-stat-item:hover {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.hero-icon-section {
  display: flex;
  align-items: center;
}

.hero-info-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 0;
}

.hero-name {
  font-weight: bold;
  color: #ffffff;
  font-size: 0.9rem;
  flex: 1;
}

.hero-traits {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.powerup-rating-section {
  display: flex;
  align-items: center;
}

.trait-tag {
  padding: 0.125rem 0.5rem;
  background: rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 4px;
  color: #93c5fd;
  font-size: 0.7rem;
  white-space: nowrap;
}

.powerup-rating-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  min-width: 80px;
}

.tier-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-weight: bold;
  font-size: 0.9rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.rating-details {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.75rem;
  text-align: center;
  max-width: 100px;
  word-wrap: break-word;
}

.hero-stats-section {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  min-width: 100px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
}

.stat-label {
  color: rgba(255, 255, 255, 0.6);
}

.stat-value {
  color: #ffffff;
  font-weight: 500;
}

/* === 无数据状态 === */
.no-hero-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

.no-data-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-data-hint {
  margin-top: 0.5rem;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.5);
  max-width: 400px;
}

/* === 响应式设计 === */
@media (max-width: 768px) {
  .powerup-header {
    /* 保持flex-direction: row，确保名称和评级始终在同一行 */
    flex-direction: row;
    align-items: center;
    justify-content: center; /* 小屏幕上也居中对齐 */
    gap: 0.75rem; /* 小屏幕上稍微减小间距 */
  }

  .powerup-name {
    font-size: 1.4rem; /* 在小屏幕上稍微减小字体 */
  }

  .tier-badge-large {
    font-size: 1rem; /* 在小屏幕上稍微减小评级标签字体 */
    padding: 0.4rem 0.8rem;
  }

  .hero-stat-item {
    grid-template-columns: 1fr;
    gap: 0.75rem;
    text-align: center;
  }

  .attributes-grid {
    grid-template-columns: 1fr;
  }

  .powerup-detail-content {
    padding: 1rem 1rem 1rem 1rem; /* 在小屏幕上减少padding */
  }
}

/* === 极小屏幕适配 === */
@media (max-width: 480px) {
  .powerup-name {
    font-size: 1.2rem; /* 进一步减小字体 */
  }

  .tier-badge-large {
    font-size: 0.9rem;
    padding: 0.3rem 0.6rem;
  }

  .powerup-detail-content {
    padding: 0.75rem 0.75rem 1rem 0.75rem;
  }
}
</style>
