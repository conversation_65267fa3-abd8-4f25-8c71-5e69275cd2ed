{"name": "tft-updater", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build"}, "dependencies": {"vue": "^3.5.17", "axios": "^1.7.9"}, "devDependencies": {"@tauri-apps/cli": "^2.0.0", "@vitejs/plugin-vue": "^6.0.0", "typescript": "~5.8.0", "vite": "^7.0.0", "vue-tsc": "^2.2.10"}}