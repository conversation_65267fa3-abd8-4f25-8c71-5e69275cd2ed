<template>
  <!-- 海克斯页面内容 -->
  <div class="hex-page">
    
    <!-- 搜索和筛选区域 -->
    <div class="search-filter-section">
      <!-- 搜索框 -->
      <div class="search-area">
        <input 
          v-model="searchQuery"
          type="text" 
          placeholder="搜索海克斯名称..."
          class="search-input"
        />
      </div>
      
      <!-- 筛选区域 -->
      <div class="filter-area">
        <button 
          v-for="filter in categoryFilters" 
          :key="filter.value"
          class="category-filter-button"
          :class="{ 'active': selectedCategory === filter.value }"
          :data-category="filter.value"
          @click="toggleCategoryFilter(filter.value)"
        >
          {{ filter.label }}
        </button>
      </div>
    </div>

    <!-- 海克斯列表区域 -->
    <div class="hex-list-area" @scroll="handleScroll">
      
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>正在加载海克斯数据...</p>
      </div>

      <!-- 暂无数据提示 -->
      <div v-else-if="allHexes.length === 0" class="no-data-state">
        <div class="no-data-icon">🔮</div>
        <h3>暂无海克斯数据</h3>
        <p>数据库连接正常，但暂未加载到海克斯数据</p>
        <button @click="loadHexes" class="retry-button">重新加载</button>
      </div>

      <!-- 海克斯分组显示 -->
      <div v-else class="hex-groups" ref="hexGroupsRef">
        
        <!-- 浮动评级指示器 -->
        <div v-if="currentVisibleTier" class="floating-tier-indicator">
          <div 
            class="tier-indicator-badge"
            :class="getTierIndicatorClass(currentVisibleTier)"
          >
            {{ currentVisibleTier }}级
          </div>
        </div>
        
        <!-- 海克斯分组内容 -->
        <div
          v-for="tier in visibleTiers"
          :key="tier"
          class="tier-group"
          :data-tier="tier"
        >
          <!-- 海克斯网格 -->
          <div class="hex-grid">
            <div
              v-for="hex in hexesByTier[tier]"
              :key="getHexKey(hex)"
              class="hex-card"
              :class="getHexLevelClass(hex.level)"
              @mouseenter="handleHexMouseEnter(hex, $event)"
              @mouseleave="handleHexMouseLeave"
              @mousemove="handleHexMouseMove"
            >
              <!-- 图标区域 -->
              <HexIcon
                :hex-name="hex.name || '未知海克斯'"
                :icon-path="hex.icon_path"
                :tier="hex.tier"
                :size="58"
                :show-tier-badge="false"
                :clickable="false"
              />

              <!-- 海克斯信息 -->
              <div class="hex-info">
                <p class="hex-name">{{ hex.name || '未知海克斯' }}</p>
              </div>


            </div>
          </div>
        </div>

        <!-- 渐进式加载指示器 -->
        <div v-if="isProgressiveLoading" class="progressive-loading">
          <div class="loading-spinner-small"></div>
          <span>正在加载更多海克斯...</span>
        </div>
      </div>
    </div>
    
    <!-- 悬停提示框 -->
    <Teleport to="body">
      <div 
        v-if="showTooltip && hoveredHex"
        class="hex-tooltip"
        :style="{ 
          left: tooltipPosition.x + 'px', 
          top: tooltipPosition.y + 'px' 
        }"
      >
        <div class="tooltip-header">
          <h4 class="tooltip-title">{{ hoveredHex.name }}</h4>
        </div>
        <div 
          v-if="hoveredHex.description"
          class="tooltip-description"
        >
          {{ hoveredHex.description }}
        </div>
        <div 
          v-else
          class="tooltip-description no-description"
        >
          暂无详细说明
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted, Teleport } from 'vue'
import HexIcon from '@/components/common/HexIcon.vue'
import { cleanIconCache, getSmartIconPath } from '@/utils/iconUtils'
import { useData } from '@/composables/useData'
import { useDataStore } from '@/stores/data'

// 海克斯数据接口
interface HexData {
  name: string
  tier: string
  icon_url?: string
  icon_path?: string
  description?: string
  level?: string
}

// === 数据组合函数 ===
const { getHexList } = useData()

// === 海克斯数据状态 ===
const allHexes = ref<HexData[]>([])
const isLoading = ref(true)
const searchQuery = ref('')
const debouncedSearchQuery = ref('')
const selectedCategory = ref<string>('all')
const currentVisibleTier = ref<string>('')
const hexGroupsRef = ref<HTMLElement | null>(null)

// 渐进式渲染状态
const immediateRenderCount = ref(20) // 立即渲染前20个
const renderedHexes = ref<HexData[]>([]) // 当前已渲染的海克斯
const isProgressiveLoading = ref(false)

// 悬停提示状态
const hoveredHex = ref<HexData | null>(null)
const tooltipPosition = ref({ x: 0, y: 0 })
const showTooltip = ref(false)

// === 分类筛选配置 ===
const categoryFilters = [
  { label: '全部', value: 'all' },
  { label: '白银', value: 'silver' },
  { label: '黄金', value: 'gold' },
  { label: '棱彩', value: 'prismatic' }
]

// === 计算属性 ===
const filteredHexes = computed(() => {
  // 使用渐进式渲染的数据而不是全部数据
  const hexes = renderedHexes.value
  const query = debouncedSearchQuery.value.toLowerCase()
  const category = selectedCategory.value

  // 如果没有筛选条件，直接返回原数组
  if (!query && category === 'all') {
    return hexes
  }

  // 使用更高效的过滤逻辑，减少函数调用开销
  const result = []
  for (const hex of hexes) {
    // 搜索筛选 - 提前退出
    if (query && !hex.name?.toLowerCase().includes(query)) {
      continue
    }

    // 分类筛选 - 优化字符串比较
    if (category !== 'all') {
      const level = hex.level || ''
      let matches = false

      switch (category) {
        case 'silver':
          matches = level.includes('一级') || level.includes('1级')
          break
        case 'gold':
          matches = level.includes('二级') || level.includes('2级')
          break
        case 'prismatic':
          matches = level.includes('三级') || level.includes('3级')
          break
      }

      if (!matches) continue
    }

    result.push(hex)
  }

  return result
})

// 为每个海克斯添加唯一key，避免重复渲染
const getHexKey = (hex: HexData) => {
  return `${hex.name}-${hex.tier}-${hex.icon_path || 'no-icon'}`
}

// 优化：预计算所有评级的海克斯数据，避免重复计算
const hexesByTier = computed(() => {
  const result: Record<string, HexData[]> = {}
  const allTiers = ['S', 'A', 'B', 'C', 'D']

  // 一次性分组所有海克斯，避免重复过滤
  for (const tier of allTiers) {
    result[tier] = filteredHexes.value
      .filter(hex => hex.tier === tier)
      .sort((a, b) => (a.name || '').localeCompare(b.name || ''))
  }

  return result
})



// 计算应该显示的评级组
const visibleTiers = computed(() => {
  const allTiers = ['S', 'A', 'B', 'C', 'D']

  return allTiers.filter(tier => {
    const hexesInTier = hexesByTier.value[tier]
    // 只要该评级组有海克斯就显示
    return hexesInTier && hexesInTier.length > 0
  })
})

// === 分类筛选功能 ===
const toggleCategoryFilter = (category: string) => {
  selectedCategory.value = category
  console.log('当前选择的分类:', selectedCategory.value)
}

// === 样式辅助函数 ===

const getTierIndicatorClass = (tier: string) => {
  const tierClasses = {
    'S': 'indicator-tier-s',
    'A': 'indicator-tier-a',
    'B': 'indicator-tier-b',
    'C': 'indicator-tier-c',
    'D': 'indicator-tier-d'
  }
  return tierClasses[tier as keyof typeof tierClasses] || ''
}



// 海克斯页面不再支持点击详情功能

// === 滚动处理 ===
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  
  // 查找当前可见的评级组
  const tierGroups = target.querySelectorAll('.tier-group')
  let visibleTier = ''
  
  for (const group of tierGroups) {
    const rect = group.getBoundingClientRect()
    const containerRect = target.getBoundingClientRect()
    
    // 如果评级组在可视区域内（主要部分可见）
    if (rect.top < containerRect.top + containerRect.height * 0.6 && rect.bottom > containerRect.top + containerRect.height * 0.4) {
      visibleTier = (group as HTMLElement).dataset.tier || ''
      break
    }
  }
  
  currentVisibleTier.value = visibleTier
  console.log('当前可见评级:', visibleTier)
}

// 海克斯点击功能已移除 - 海克斯页面不提供详情功能

// === 悬停处理 ===
const handleHexMouseEnter = (hex: HexData, event: MouseEvent) => {
  // 只要有海克斯数据就显示悬停提示
  hoveredHex.value = hex
  updateTooltipPosition(event)
  showTooltip.value = true
}

const handleHexMouseLeave = () => {
  showTooltip.value = false
  hoveredHex.value = null
}

const handleHexMouseMove = (event: MouseEvent) => {
  if (showTooltip.value) {
    updateTooltipPosition(event)
  }
}

const updateTooltipPosition = (event: MouseEvent) => {
  const windowWidth = window.innerWidth
  const tooltipWidth = 300 // 提示框的大概宽度
  
  // 如果鼠标在屏幕右半部分，则将提示框显示在左侧
  const showOnLeft = event.clientX > windowWidth / 2
  
  tooltipPosition.value = {
    x: showOnLeft ? event.clientX - tooltipWidth - 10 : event.clientX + 10,
    y: event.clientY - 10
  }
}

// 获取海克斯等级样式类
const getHexLevelClass = (level: string | undefined) => {
  if (!level) return ''

  if (level.includes('一级') || level.includes('1级')) {
    return 'hex-level-1'
  } else if (level.includes('二级') || level.includes('2级')) {
    return 'hex-level-2'
  } else if (level.includes('三级') || level.includes('3级')) {
    return 'hex-level-3'
  }
  return ''
}

// === 渐进式渲染函数 ===
const startProgressiveRender = (allData: HexData[]) => {
  console.log(`🚀 开始渐进式渲染，总共${allData.length}个海克斯`)

  // 立即渲染前20个海克斯
  const immediateHexes = allData.slice(0, immediateRenderCount.value)
  renderedHexes.value = immediateHexes
  console.log(`⚡ 立即渲染前${immediateHexes.length}个海克斯`)

  // 预加载这些海克斯的图标
  preloadVisibleIcons(immediateHexes)

  // 如果还有更多数据，分批渲染剩余的
  if (allData.length > immediateRenderCount.value) {
    isProgressiveLoading.value = true
    setTimeout(() => {
      renderRemainingHexes(allData)
    }, 50) // 50ms后开始渲染剩余的
  }
}

const renderRemainingHexes = (allData: HexData[]) => {
  const remaining = allData.slice(immediateRenderCount.value)
  const batchSize = 30 // 每批渲染30个
  let currentIndex = 0

  const renderBatch = () => {
    if (currentIndex >= remaining.length) {
      isProgressiveLoading.value = false
      console.log(`✅ 渐进式渲染完成，总共渲染${allData.length}个海克斯`)
      return
    }

    const batch = remaining.slice(currentIndex, currentIndex + batchSize)
    renderedHexes.value = [...renderedHexes.value, ...batch]
    currentIndex += batchSize

    console.log(`📦 渲染批次: ${currentIndex}/${remaining.length}`)

    // 继续下一批
    setTimeout(renderBatch, 16) // 16ms ≈ 60fps
  }

  renderBatch()
}

// === 智能图标预加载 ===
// 预加载可见区域的图标（最高优先级）
const preloadVisibleIcons = async (hexes: HexData[]) => {
  // 立即预加载前15个（首屏可见）
  const visibleHexes = hexes.slice(0, 15)
  console.log(`🖼️ 开始预加载首屏${visibleHexes.length}个海克斯图标...`)

  // 分批加载，每批5个，避免并发过多
  const batchSize = 5
  for (let i = 0; i < visibleHexes.length; i += batchSize) {
    const batch = visibleHexes.slice(i, i + batchSize)

    await Promise.allSettled(
      batch.map(hex => {
        if (hex.icon_path) {
          return getSmartIconPath(hex.icon_path)
        }
        return Promise.resolve(null)
      })
    )

    // 每批之间稍作延迟，避免阻塞UI
    if (i + batchSize < visibleHexes.length) {
      await new Promise(resolve => setTimeout(resolve, 50))
    }
  }

  console.log(`✅ 首屏图标预加载完成`)

  // 延迟预加载次要可见区域
  setTimeout(() => preloadSecondaryIcons(hexes.slice(15, 35)), 200)
}

// 预加载次要可见区域图标（中等优先级）
const preloadSecondaryIcons = async (hexes: HexData[]) => {
  if (hexes.length === 0) return

  console.log(`🖼️ 预加载次要区域${hexes.length}个海克斯图标...`)

  // 分批加载，每批3个
  const batchSize = 3
  for (let i = 0; i < hexes.length; i += batchSize) {
    const batch = hexes.slice(i, i + batchSize)

    await Promise.allSettled(
      batch.map(hex => {
        if (hex.icon_path) {
          return getSmartIconPath(hex.icon_path)
        }
        return Promise.resolve(null)
      })
    )

    // 每批之间延迟更长，降低优先级
    if (i + batchSize < hexes.length) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }

  console.log(`✅ 次要区域图标预加载完成`)

  // 继续预加载剩余图标
  setTimeout(() => preloadRemainingIcons(hexes.slice(35)), 500)
}

// 预加载剩余图标（最低优先级）
const preloadRemainingIcons = async (hexes: HexData[]) => {
  if (hexes.length === 0) return

  console.log(`🖼️ 后台预加载剩余${hexes.length}个海克斯图标...`)

  // 分批加载，每批2个，优先级最低
  const batchSize = 2
  for (let i = 0; i < hexes.length; i += batchSize) {
    const batch = hexes.slice(i, i + batchSize)

    await Promise.allSettled(
      batch.map(hex => {
        if (hex.icon_path) {
          return getSmartIconPath(hex.icon_path)
        }
        return Promise.resolve(null)
      })
    )

    // 每批之间延迟最长，确保不影响用户操作
    if (i + batchSize < hexes.length) {
      await new Promise(resolve => setTimeout(resolve, 200))
    }
  }

  console.log(`✅ 所有图标预加载完成`)
}

// === 数据加载 ===
const loadHexes = async () => {
  const startTime = performance.now()
  console.log('🔍 开始加载海克斯数据...')

  try {
    // 先尝试同步获取缓存数据，避免设置loading状态
    const dataStore = useDataStore()
    const cachedData = dataStore.getCachedQuery('hex_list_fast')

    console.log('🔍 详细缓存检查:', {
      hasCachedData: !!cachedData,
      dataLength: cachedData?.length || 0,
      cacheKeys: Object.keys(dataStore.queryCache),
      allCacheData: dataStore.queryCache
    })

    if (cachedData && cachedData.length > 0) {
      const loadTime = performance.now() - startTime
      console.log(`✅ 从缓存即时加载海克斯数据，零延迟，共${cachedData.length}条，耗时${loadTime.toFixed(2)}ms`)

      // 保存全部数据
      allHexes.value = cachedData

      // 立即开始渐进式渲染
      startProgressiveRender(cachedData)

      const renderTime = performance.now() - startTime
      console.log(`🎨 前${immediateRenderCount.value}个海克斯立即渲染完成，总耗时${renderTime.toFixed(2)}ms`)
      return
    }

    // 缓存未命中时才显示loading
    console.log('缓存未命中，显示loading并从数据库加载...')
    isLoading.value = true

    const hexData = await getHexList()
    allHexes.value = hexData || []

    if (allHexes.value.length > 0) {
      console.log(`✅ 从数据库加载 ${allHexes.value.length} 个海克斯`)
      // 开始渐进式渲染
      startProgressiveRender(allHexes.value)
    }

  } catch (error) {
    console.error('❌ 加载海克斯数据失败:', error)
    allHexes.value = []
  } finally {
    isLoading.value = false
  }
}

// === 搜索防抖处理 ===
let searchTimeout: ReturnType<typeof setTimeout> | null = null

watch(searchQuery, (newValue) => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  searchTimeout = setTimeout(() => {
    debouncedSearchQuery.value = newValue
  }, 100) // 增加防抖延迟，减少计算频率
})

// === 组件挂载时加载数据 ===
onMounted(() => {
  // 初始化防抖搜索值
  debouncedSearchQuery.value = searchQuery.value
  
  loadHexes()
  // 初始化时设置第一个可见的评级
  setTimeout(() => {
    if (visibleTiers.value.length > 0) {
      currentVisibleTier.value = visibleTiers.value[0]
    }
  }, 100)
})

// === 组件卸载时清理缓存 ===
onUnmounted(() => {
  // 清理图标缓存，避免内存泄漏
  cleanIconCache(100)
})
</script>

<style scoped>
/* === 海克斯页面 === */
.hex-page {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  gap: 1rem;
  overflow: hidden;
}

/* === 搜索和筛选区域 === */
.search-filter-section {
  flex-shrink: 0;
  display: flex;
  gap: 1rem;
  align-items: center;
}

.search-area {
  flex: 1;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.search-input:focus {
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

/* === 筛选区域 === */
.filter-area {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.category-filter-button {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.category-filter-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.category-filter-button:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  transform: translateY(-1px);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.category-filter-button:hover::before {
  left: 100%;
}

.category-filter-button.active {
  color: white;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  transform: translateY(-2px);
}

/* 全部按钮激活状态 */
.category-filter-button.active[data-category="all"] {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

/* 白银按钮激活状态 */
.category-filter-button.active[data-category="silver"] {
  background: linear-gradient(135deg, rgba(192, 192, 192, 0.8), rgba(169, 169, 169, 0.8));
  border-color: #c0c0c0;
  box-shadow: 0 0 15px rgba(192, 192, 192, 0.4);
}

/* 黄金按钮激活状态 */
.category-filter-button.active[data-category="gold"] {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.8), rgba(255, 193, 7, 0.8));
  border-color: #ffd700;
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.4);
}

/* 棱彩按钮激活状态 */
.category-filter-button.active[data-category="prismatic"] {
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.8), rgba(75, 0, 130, 0.8));
  border-color: #8a2be2;
  box-shadow: 0 0 15px rgba(138, 43, 226, 0.4);
}

/* 移除了旧的评级筛选按钮样式 */

/* === 海克斯列表区域 === */
.hex-list-area {
  flex: 1;
  overflow-y: auto;
  padding-right: 0.5rem;
  position: relative;
}

.hex-list-area::-webkit-scrollbar {
  width: 6px;
}

.hex-list-area::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.hex-list-area::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.hex-list-area::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* === 加载状态 === */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: rgba(255, 255, 255, 0.8);
  gap: 1rem;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* === 渐进式加载指示器 === */
.progressive-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* === 暂无数据状态 === */
.no-data-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: rgba(255, 255, 255, 0.8);
  gap: 1rem;
}

.no-data-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.no-data-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.no-data-state p {
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  margin-bottom: 1rem;
}

.retry-button {
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* === 浮动评级指示器 === */
.floating-tier-indicator {
  position: fixed;
  left: 50%;
  top: 12rem;
  transform: translateX(-50%);
  z-index: 1000;
  pointer-events: none;
}

.tier-indicator-badge {
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.indicator-tier-s { background: linear-gradient(135deg, rgba(255, 107, 157, 0.9), rgba(255, 71, 87, 0.9)); }
.indicator-tier-a { background: linear-gradient(135deg, rgba(255, 167, 38, 0.9), rgba(255, 143, 0, 0.9)); }
.indicator-tier-b { background: linear-gradient(135deg, rgba(255, 235, 59, 0.9), rgba(255, 193, 7, 0.9)); }
.indicator-tier-c { background: linear-gradient(135deg, rgba(205, 220, 57, 0.9), rgba(139, 195, 74, 0.9)); }
.indicator-tier-d { background: linear-gradient(135deg, rgba(76, 175, 80, 0.9), rgba(46, 125, 50, 0.9)); }

/* === 海克斯分组 === */
.hex-groups {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* 移除了过渡动画相关样式 */

.tier-group {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(8px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  overflow: hidden;
  min-height: 120px;
  position: relative;
}

.tier-group::before {
  content: attr(data-tier) '级';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rem;
  display: flex;
  align-items: center;
  padding-left: 0.75rem;
  font-size: 14px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  z-index: 5;
  border-radius: 12px 12px 0 0;
}

.tier-group[data-tier="S"]::before {
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.9), rgba(255, 71, 87, 0.9));
  border-bottom: 2px solid rgba(255, 107, 157, 0.8);
}

.tier-group[data-tier="A"]::before {
  background: linear-gradient(135deg, rgba(255, 167, 38, 0.9), rgba(255, 143, 0, 0.9));
  border-bottom: 2px solid rgba(255, 167, 38, 0.8);
}

.tier-group[data-tier="B"]::before {
  background: linear-gradient(135deg, rgba(255, 235, 59, 0.9), rgba(255, 193, 7, 0.9));
  border-bottom: 2px solid rgba(255, 235, 59, 0.8);
}

.tier-group[data-tier="C"]::before {
  background: linear-gradient(135deg, rgba(205, 220, 57, 0.9), rgba(139, 195, 74, 0.9));
  border-bottom: 2px solid rgba(205, 220, 57, 0.8);
}

.tier-group[data-tier="D"]::before {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.9), rgba(46, 125, 50, 0.9));
  border-bottom: 2px solid rgba(76, 175, 80, 0.8);
}

/* 移除了评级标签样式，现在使用浮动指示器和组标题 */

/* === 海克斯网格 === */
.hex-grid {
  padding: 2.75rem 0.5rem 0.5rem 0.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
  gap: 0.375rem;
  align-content: start;
}

/* === 海克斯卡片 === */
.hex-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  position: relative;
  z-index: 1;
  padding: 0.5rem 0.375rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.hex-card:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px) scale(1.02);
  z-index: 2;
}

/* 海克斯等级样式 */
.hex-card.hex-level-1:hover {
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 4px 16px rgba(255, 255, 255, 0.2);
}

.hex-card.hex-level-2:hover {
  border-color: rgba(255, 215, 0, 0.6);
  box-shadow: 0 4px 16px rgba(255, 215, 0, 0.3);
}

.hex-card.hex-level-3:hover {
  border-color: rgba(138, 43, 226, 0.6);
  box-shadow: 0 4px 16px rgba(138, 43, 226, 0.3);
}





/* === 海克斯信息 === */
.hex-info {
  margin-top: 0.375rem;
  width: 100%;
}

.hex-name {
  color: white;
  font-size: 10px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  text-align: center;
  word-wrap: break-word;
  line-height: 1.2;
  max-height: 2.4em;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  background: rgba(0, 0, 0, 0.2);
  padding: 0.25rem 0.125rem;
  border-radius: 4px;
  margin: 0;
}

/* === 悬停提示框 === */
.hex-tooltip {
  position: fixed;
  z-index: 9999;
  max-width: 300px;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1rem;
  color: white;
  font-size: 14px;
  line-height: 1.4;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  pointer-events: none;
  animation: tooltipFadeIn 0.2s ease-out;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.tooltip-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  gap: 0.5rem;
}

.tooltip-title {
  font-size: 16px;
  font-weight: 700;
  color: white;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}



.tooltip-description {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  word-wrap: break-word;
}

.tooltip-description.no-description {
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}


</style>