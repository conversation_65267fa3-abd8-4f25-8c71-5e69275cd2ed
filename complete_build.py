#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
弈秒决完整打包脚本 v2.0
处理三个程序的完整打包：主程序(Tauri)、更新器(Tauri)、Python模块(PyInstaller)
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import json
import time

class CompleteBuildSystem:
    def __init__(self):
        self.root_dir = Path.cwd()
        self.build_dir = self.root_dir / "complete_build"
        self.dist_dir = self.root_dir / "final_dist"
        
        # 清理构建目录
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
        self.build_dir.mkdir()
        
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
        self.dist_dir.mkdir()
        
        # 项目路径
        self.main_app_dir = self.root_dir / "TFT常驻助手"
        self.updater_dir = self.root_dir / "updater"
        self.python_dir = self.root_dir / "TFT常驻助手" / "src-tauri" / "resources" / "yimiaojue"
        
    def log(self, message):
        """打印日志"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
        
    def run_command(self, command, cwd=None, shell=True, env=None):
        """执行命令"""
        self.log(f"执行命令: {command}")
        self.log(f"工作目录: {cwd or self.root_dir}")

        if env is None:
            env = os.environ.copy()

        result = subprocess.run(
            command,
            shell=shell,
            cwd=cwd or self.root_dir,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='ignore',  # 忽略编码错误
            env=env
        )

        if result.returncode != 0:
            self.log(f"命令执行失败:")
            self.log(f"STDOUT: {result.stdout}")
            self.log(f"STDERR: {result.stderr}")
            raise Exception(f"命令执行失败: {command}")

        return result.stdout
        
    def clean_build_directories(self):
        """清理旧的构建目录（保留Rust编译缓存）"""
        self.log("=" * 50)
        self.log("清理旧的构建目录...")

        # 只清理输出目录，保留Rust的target缓存以加速编译
        dirs_to_clean = [
            self.root_dir / "build",
            self.root_dir / "dist"
        ]

        for dir_path in dirs_to_clean:
            if dir_path.exists():
                self.log(f"删除目录: {dir_path}")
                shutil.rmtree(dir_path, ignore_errors=True)

        # 只清理Rust target目录中的release构建结果，保留依赖缓存
        rust_release_dirs = [
            self.main_app_dir / "src-tauri" / "target" / "release",
            self.updater_dir / "src-tauri" / "target" / "release"
        ]

        for release_dir in rust_release_dirs:
            if release_dir.exists():
                self.log(f"清理Rust release目录: {release_dir}")
                # 只删除exe文件和bundle目录，保留依赖缓存
                for item in release_dir.iterdir():
                    if item.is_file() and item.suffix == '.exe':
                        item.unlink()
                        self.log(f"删除旧的exe文件: {item.name}")
                    elif item.is_dir() and item.name == "bundle":
                        shutil.rmtree(item, ignore_errors=True)
                        self.log(f"删除bundle目录: {item.name}")

        self.log("构建目录清理完成（保留Rust编译缓存）")

    def should_install_npm_deps(self, project_dir):
        """检查是否需要安装npm依赖"""
        package_json = project_dir / "package.json"
        package_lock = project_dir / "package-lock.json"
        node_modules = project_dir / "node_modules"

        # 如果node_modules不存在，需要安装
        if not node_modules.exists():
            return True

        # 如果package.json比package-lock.json新，需要安装
        if package_json.exists() and package_lock.exists():
            if package_json.stat().st_mtime > package_lock.stat().st_mtime:
                return True

        # 如果package.json比node_modules新，需要安装
        if package_json.exists() and node_modules.exists():
            if package_json.stat().st_mtime > node_modules.stat().st_mtime:
                return True

        return False

    def build_python_module(self):
        """打包Python模块（onedir模式，不包含外置数据）"""
        self.log("=" * 50)
        self.log("开始打包Python模块...")

        if not self.python_dir.exists():
            raise Exception(f"Python目录不存在: {self.python_dir}")

        # 检查spec文件
        spec_file = self.root_dir / "YimiaoJue.spec"
        if not spec_file.exists():
            raise Exception(f"YimiaoJue.spec文件不存在: {spec_file}")

        # 设置环境变量以处理编码问题
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONDONTWRITEBYTECODE'] = '1'

        # 使用PyInstaller打包
        self.run_command(f'python -m PyInstaller "{spec_file}" --noconfirm', env=env)

        # 检查打包结果
        pyinstaller_output = self.root_dir / "dist" / "YimiaoJue"
        if not pyinstaller_output.exists():
            raise Exception("PyInstaller打包失败，未找到输出目录")

        # 复制到构建目录
        python_build_dir = self.build_dir / "python_module"
        if python_build_dir.exists():
            shutil.rmtree(python_build_dir)
        shutil.copytree(pyinstaller_output, python_build_dir)

        self.log(f"Python模块打包完成: {python_build_dir}")
        return python_build_dir
        
    def build_main_app(self, python_build_dir):
        """构建主程序 (TFT常驻助手)"""
        self.log("=" * 50)
        self.log("开始构建主程序...")

        if not self.main_app_dir.exists():
            raise Exception(f"主程序目录不存在: {self.main_app_dir}")

        # 检查package.json
        package_json = self.main_app_dir / "package.json"
        if not package_json.exists():
            raise Exception(f"package.json不存在: {package_json}")

        # 检查并安装依赖
        self.log("检查主程序依赖...")
        if self.should_install_npm_deps(self.main_app_dir):
            self.log("安装主程序依赖...")
            self.run_command("npm install", cwd=self.main_app_dir)
        else:
            self.log("依赖已是最新，跳过安装")

        # 构建Tauri应用
        self.log("构建Tauri主程序...")
        try:
            self.run_command("npm run tauri build", cwd=self.main_app_dir)
        except Exception as e:
            # 如果构建失败，检查是否已经有exe文件生成
            self.log(f"构建过程出现错误，检查是否有exe文件生成: {e}")

        # 查找构建结果
        tauri_target = self.main_app_dir / "src-tauri" / "target" / "release"
        exe_files = list(tauri_target.glob("*.exe"))

        if not exe_files:
            raise Exception("主程序构建失败，未找到exe文件")

        main_exe = exe_files[0]  # 取第一个exe文件
        self.log(f"找到主程序exe文件: {main_exe.name}")

        # 复制到构建目录
        main_build_dir = self.build_dir / "main_app"
        if main_build_dir.exists():
            shutil.rmtree(main_build_dir)
        main_build_dir.mkdir(parents=True)

        # 复制主程序exe
        shutil.copy2(main_exe, main_build_dir / main_exe.name)

        # 复制Python模块到主程序目录下的yimiaojue文件夹
        yimiaojue_target = main_build_dir / "yimiaojue"
        if yimiaojue_target.exists():
            shutil.rmtree(yimiaojue_target)
        shutil.copytree(python_build_dir, yimiaojue_target)

        self.log(f"主程序构建完成: {main_build_dir}")
        self.log(f"Python模块已复制到: {yimiaojue_target}")
        return main_build_dir
        
    def build_updater(self):
        """构建更新器"""
        self.log("=" * 50)
        self.log("开始构建更新器...")
        
        if not self.updater_dir.exists():
            raise Exception(f"更新器目录不存在: {self.updater_dir}")
            
        # 检查并安装依赖
        self.log("检查更新器依赖...")
        if self.should_install_npm_deps(self.updater_dir):
            self.log("安装更新器依赖...")
            self.run_command("npm install", cwd=self.updater_dir)
        else:
            self.log("依赖已是最新，跳过安装")
        
        # 构建Tauri应用
        self.log("构建Tauri更新器...")
        self.run_command("npm run tauri build", cwd=self.updater_dir)
        
        # 查找构建结果
        tauri_target = self.updater_dir / "src-tauri" / "target" / "release"
        exe_files = list(tauri_target.glob("*.exe"))
        
        if not exe_files:
            raise Exception("更新器构建失败，未找到exe文件")
            
        updater_exe = exe_files[0]
        
        # 复制到构建目录
        updater_build_dir = self.build_dir / "updater"
        updater_build_dir.mkdir()
        shutil.copy2(updater_exe, updater_build_dir / "弈秒决更新器.exe")
        
        self.log(f"更新器构建完成: {updater_build_dir}")
        return updater_build_dir
        
    def copy_external_data(self):
        """复制外置数据文件"""
        self.log("=" * 50)
        self.log("复制外置数据文件...")
        
        data_build_dir = self.build_dir / "external_data"
        data_build_dir.mkdir()
        
        # 复制数据库文件（排除在初始包中）
        db_file = self.root_dir / "tft_data.db"
        if db_file.exists():
            # 不复制到构建包中，由更新器下载
            self.log("数据库文件将由更新器下载，跳过复制")
        
        # 复制图标目录（排除在初始包中）
        icon_dirs = ["英雄图标", "装备图标", "羁绊图标", "海克斯数据"]
        for icon_dir in icon_dirs:
            src_dir = self.root_dir / icon_dir
            if src_dir.exists():
                # 不复制到构建包中，由更新器下载
                self.log(f"图标目录 {icon_dir} 将由更新器下载，跳过复制")
                
        # 创建空的数据目录结构
        for icon_dir in icon_dirs:
            (data_build_dir / icon_dir).mkdir(exist_ok=True)
            
        # 创建占位符文件
        placeholder_content = "此目录的内容将在首次运行时由更新器自动下载"
        for icon_dir in icon_dirs:
            placeholder_file = data_build_dir / icon_dir / "README.txt"
            with open(placeholder_file, 'w', encoding='utf-8') as f:
                f.write(placeholder_content)
                
        self.log(f"外置数据目录结构创建完成: {data_build_dir}")
        return data_build_dir
        
    def create_final_package(self, main_build_dir, updater_build_dir, data_build_dir):
        """创建最终发布包"""
        self.log("=" * 50)
        self.log("创建最终发布包...")

        # 创建发布目录结构
        release_dir = self.dist_dir / "弈秒决_完整版"
        if release_dir.exists():
            shutil.rmtree(release_dir)
        release_dir.mkdir(parents=True)

        # 复制更新器（作为主入口）
        updater_exe = list(updater_build_dir.glob("*.exe"))[0]
        shutil.copy2(updater_exe, release_dir / "弈秒决更新器.exe")
        self.log(f"复制更新器: {updater_exe.name}")

        # 复制主程序目录（包含Python模块）
        for item in main_build_dir.iterdir():
            if item.is_file() and item.suffix == '.exe':
                shutil.copy2(item, release_dir / "TFT常驻助手.exe")
                self.log(f"复制主程序: {item.name}")
            elif item.is_dir() and item.name == "yimiaojue":
                shutil.copytree(item, release_dir / "yimiaojue")
                self.log(f"复制Python模块: {item.name}")

        # 复制外置数据目录结构（空目录结构）
        if data_build_dir.exists():
            for item in data_build_dir.iterdir():
                if item.is_dir():
                    shutil.copytree(item, release_dir / item.name)
                    self.log(f"复制数据目录结构: {item.name}")
                else:
                    shutil.copy2(item, release_dir / item.name)
                    
        # 创建启动器脚本
        self.create_launcher_script(release_dir)
        
        # 创建说明文件
        self.create_readme(release_dir)
        
        # 创建压缩包
        self.create_zip_package(release_dir)
        
        self.log(f"最终发布包创建完成: {release_dir}")
        return release_dir
        
    def create_launcher_script(self, release_dir):
        """创建启动器脚本"""
        launcher_content = '''@echo off
chcp 65001 > nul
cd /d "%~dp0"

echo 启动弈秒决TFT助手...
echo.

if exist "弈秒决更新器.exe" (
    echo 使用更新器启动（推荐）...
    start "" "弈秒决更新器.exe"
) else if exist "TFT常驻助手.exe" (
    echo 直接启动主程序...
    start "" "TFT常驻助手.exe"
) else (
    echo 错误：未找到可执行文件！
    pause
)
'''
        
        launcher_path = release_dir / "启动弈秒决.bat"
        with open(launcher_path, 'w', encoding='utf-8') as f:
            f.write(launcher_content)
            
    def create_readme(self, release_dir):
        """创建说明文件"""
        readme_content = '''# 弈秒决 TFT助手 完整版

## 目录结构
- 启动弈秒决.bat - 主启动器（推荐使用）
- 弈秒决更新器.exe - 更新器程序（检查更新并启动）
- TFT常驻助手.exe - 主程序（Vue+Rust界面）
- YimiaoJue/ - Python OCR模块（onedir打包）
- 英雄图标/ - 英雄头像（首次运行时下载）
- 装备图标/ - 装备图标（首次运行时下载）
- 羁绊图标/ - 羁绊图标（首次运行时下载）
- 海克斯数据/ - 海克斯强化数据（首次运行时下载）

## 使用说明
1. 双击"启动弈秒决.bat"启动程序
2. 首次运行会自动检查并下载最新数据
3. 程序会自动检查更新并保持最新版本

## 程序架构
- **更新器**: 负责检查更新、下载数据、启动主程序
- **主程序**: Vue+Rust界面，负责用户交互和功能调度
- **Python模块**: OCR识别核心，处理游戏画面分析

## 注意事项
- 首次运行需要网络连接下载游戏数据
- 请保持网络连接以获取最新更新
- 如有问题请联系开发者

## 手动启动选项
如果自动启动有问题，可以尝试：
1. 弈秒决更新器.exe - 检查更新并启动
2. TFT常驻助手.exe - 直接启动主界面
'''
        
        readme_path = release_dir / "README.txt"
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
            
    def create_zip_package(self, release_dir):
        """创建ZIP压缩包"""
        import zipfile
        
        zip_path = self.dist_dir / "弈秒决_完整版.zip"
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in release_dir.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(release_dir)
                    zipf.write(file_path, arcname)
                    
        self.log(f"ZIP压缩包创建完成: {zip_path}")
        return zip_path
        
    def build_all(self):
        """执行完整构建"""
        self.log("开始完整构建流程...")
        
        try:
            # 0. 清理旧的构建目录
            self.clean_build_directories()

            # 1. 打包Python模块
            python_build_dir = self.build_python_module()

            # 2. 构建主程序
            main_build_dir = self.build_main_app(python_build_dir)

            # 3. 构建更新器
            updater_build_dir = self.build_updater()

            # 4. 处理外置数据
            data_build_dir = self.copy_external_data()

            # 5. 创建最终发布包
            final_package = self.create_final_package(main_build_dir, updater_build_dir, data_build_dir)
            
            self.log("=" * 50)
            self.log("构建完成！")
            self.log(f"构建目录: {self.build_dir}")
            self.log(f"发布目录: {self.dist_dir}")
            self.log("=" * 50)
            
            return True
            
        except Exception as e:
            self.log(f"构建失败: {e}")
            return False

if __name__ == "__main__":
    builder = CompleteBuildSystem()
    success = builder.build_all()
    
    if success:
        print("\n✓ 完整构建成功！")
        print(f"发布包位置: {builder.dist_dir}")
    else:
        print("\n✗ 构建失败！")
        sys.exit(1)
