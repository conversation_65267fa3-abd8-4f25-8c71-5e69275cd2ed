# -*- mode: python ; coding: utf-8 -*-

# --- 添加项目自身所需的数据文件 ---
# 注意：数据库文件(tft_data.db)已外置，由更新器下载，不包含在打包中
datas = [
    ('TFT常驻助手/src-tauri/resources/yimiaojue/modules/hash_bases.json', 'modules'),
    ('TFT常驻助手/src-tauri/resources/yimiaojue/config.ini', '.')
]

# --- 定义需要强制包含的隐藏依赖 (现在只包含非RapidOCR的) ---
hiddenimports = [
    'pypinyin',
    'Levenshtein',
    'imagehash',
    'packaging',
    'scipy', # imagehash 需要
    'win32gui',
    'win32api'
]

# --- 定义所有需要明确排除的大型库和废弃模块 ---
excludes = [
    'PyQt5', 'PyQt6', 'PySide2', 'PySide6',
    'matplotlib', 'torch', 'tensorflow', 'sklearn',
    'notebook', 'jupyter', 'sympy', 'IPython', 'pandas',
    'updater',  # 排除废弃的updater模块
    'updater_main'  # 排除废弃的updater_main模块
]

# --- DPI 感知 Manifest ---
manifest = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <assemblyIdentity
    version="*******"
    processorArchitecture="*"
    name="YimiaoJue.exe"
    type="win32"
  />
  <description>YimiaoJue TFT Assistant</description>
  <dependency>
    <dependentAssembly>
      <assemblyIdentity
        type="win32"
        name="Microsoft.Windows.Common-Controls"
        version="*******"
        processorArchitecture="*"
        publicKeyToken="6595b64144ccf1df"
        language="*"
      />
    </dependentAssembly>
  </dependency>
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v2">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker" uiAccess="false"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <application xmlns="urn:schemas-microsoft-com:asm.v3">
    <windowsSettings>
      <dpiAware xmlns="http://schemas.microsoft.com/SMI/2005/WindowsSettings">true/PM</dpiAware>
      <dpiAwareness xmlns="http://schemas.microsoft.com/SMI/2016/WindowsSettings">PerMonitorV2, PerMonitor</dpiAwareness>
    </windowsSettings>
  </application>
</assembly>
'''


a = Analysis(
    ['TFT常驻助手\\src-tauri\\resources\\yimiaojue\\ocr查询.py'],
    pathex=['TFT常驻助手\\src-tauri\\resources\\yimiaojue'],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=['hooks'], # <--- 指定 hook 文件所在的目录
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='YimiaoJue',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='TFT常驻助手/src-tauri/resources/yimiaojue/modules/app.ico',
    manifest=manifest,
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='YimiaoJue',
)
