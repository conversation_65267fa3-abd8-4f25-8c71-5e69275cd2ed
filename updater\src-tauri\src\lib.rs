use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use std::process::Command;
use anyhow::Result;

#[derive(Debug, Serialize, Deserialize)]
struct ComponentInfo {
    name: String,
    version: String,
    local_path: String,
    remote_filename: String,
    checksum: String,
    checksum_type: String,
    size: u64,
    description: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct UpdateManifest {
    version: String,
    last_updated: String,
    download_base_url: String,
    components: HashMap<String, ComponentInfo>,
}

#[derive(Debug, Serialize, Deserialize)]
struct LocalVersionComponent {
    version: String,
    checksum: String,
    last_updated: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct LocalVersion {
    version: String,
    last_checked: String,
    components: HashMap<String, LocalVersionComponent>,
}

// 获取项目根目录路径（数据文件存放位置）
fn get_project_root() -> Result<std::path::PathBuf> {
    let exe_path = std::env::current_exe()?;
    let exe_dir = exe_path.parent().unwrap().to_path_buf();

    // 查找主程序目录的可能位置
    let search_paths = vec![
        // 更新器同级目录
        exe_dir.clone(),
        // 更新器上级目录
        exe_dir.parent().unwrap_or(&exe_dir).to_path_buf(),
        // 主程序目录（如果是onedir打包）
        exe_dir.join("main_app"),
        exe_dir.join("弈秒决"),
        exe_dir.join("YimiaoJue"),
        exe_dir.parent().unwrap_or(&exe_dir).join("main_app"),
        exe_dir.parent().unwrap_or(&exe_dir).join("弈秒决"),
        exe_dir.parent().unwrap_or(&exe_dir).join("YimiaoJue"),
    ];

    // 查找包含主程序或数据文件的目录
    for search_dir in &search_paths {
        // 检查是否包含主程序
        let main_exe_names = ["弈秒决.exe", "YimiaoJue.exe", "TFT助手.exe", "main.exe", "app.exe"];
        for exe_name in &main_exe_names {
            if search_dir.join(exe_name).exists() {
                println!("找到主程序目录: {:?}", search_dir);
                return Ok(search_dir.clone());
            }
        }

        // 检查是否包含数据文件
        if search_dir.join("tft_data.db").exists() {
            println!("找到数据目录: {:?}", search_dir);
            return Ok(search_dir.clone());
        }
    }

    // 如果都找不到，使用更新器的上级目录作为默认位置
    let default_root = exe_dir.parent().unwrap_or(&exe_dir).to_path_buf();
    println!("使用默认数据目录: {:?}", default_root);
    Ok(default_root)
}

#[tauri::command]
async fn get_local_version() -> Result<LocalVersion, String> {
    let project_root = get_project_root().map_err(|e| e.to_string())?;
    let config_path = project_root.join("config").join("local-version.json");

    if config_path.exists() {
        let content = fs::read_to_string(&config_path).map_err(|e| e.to_string())?;
        let local_version: LocalVersion = serde_json::from_str(&content).map_err(|e| e.to_string())?;
        Ok(local_version)
    } else {
        // 返回默认版本
        Ok(LocalVersion {
            version: "1.0.0".to_string(),
            last_checked: chrono::Utc::now().to_rfc3339(),
            components: HashMap::new(),
        })
    }
}

#[derive(Debug, Serialize, Deserialize)]
struct ConfigServerResponse {
    release: Option<ConfigChannel>,
    beta: Option<ConfigChannel>,
}

#[derive(Debug, Serialize, Deserialize)]
struct ConfigChannel {
    version_url: String,
    download_base_url: String,
}

#[tauri::command]
async fn get_config_from_server(url: String) -> Result<ConfigServerResponse, String> {
    let client = reqwest::Client::new();
    let response = client
        .get(&url)
        .header("Cache-Control", "no-cache")
        .send()
        .await
        .map_err(|e| format!("网络请求失败: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("HTTP错误: {}", response.status()));
    }

    let config: ConfigServerResponse = response
        .json()
        .await
        .map_err(|e| format!("解析配置JSON失败: {}", e))?;

    Ok(config)
}

#[tauri::command]
async fn get_remote_manifest(url: String) -> Result<UpdateManifest, String> {
    let client = reqwest::Client::new();
    let response = client
        .get(&url)
        .header("Cache-Control", "no-cache")
        .send()
        .await
        .map_err(|e| format!("网络请求失败: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("HTTP错误: {}", response.status()));
    }

    let manifest: UpdateManifest = response
        .json()
        .await
        .map_err(|e| format!("解析JSON失败: {}", e))?;

    Ok(manifest)
}

#[tauri::command]
async fn download_and_update_component(
    component_id: String,
    download_url: String,
    expected_checksum: String,
    local_path: String,
) -> Result<(), String> {
    let project_root = get_project_root().map_err(|e| e.to_string())?;
    let target_path = project_root.join(&local_path);

    // 下载文件
    let client = reqwest::Client::new();
    let response = client
        .get(&download_url)
        .send()
        .await
        .map_err(|e| format!("下载失败: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("下载HTTP错误: {}", response.status()));
    }

    let file_data = response
        .bytes()
        .await
        .map_err(|e| format!("读取文件数据失败: {}", e))?;

    // 验证校验和
    let actual_checksum = format!("{:x}", md5::compute(&file_data));
    if actual_checksum != expected_checksum {
        return Err(format!(
            "文件校验失败，期望: {}, 实际: {}",
            expected_checksum, actual_checksum
        ));
    }

    // 处理文件
    if local_path.ends_with(".db") {
        // 数据库文件直接替换
        if let Some(parent) = target_path.parent() {
            fs::create_dir_all(parent).map_err(|e| format!("创建目录失败: {}", e))?;
        }
        fs::write(&target_path, &file_data).map_err(|e| format!("写入文件失败: {}", e))?;
        println!("数据库文件已保存到: {:?}", target_path);
    } else if download_url.ends_with(".zip") {
        // ZIP文件需要解压到对应目录
        let extract_dir = if local_path.ends_with(".zip") {
            // 如果local_path是.zip，去掉扩展名作为目录名
            data_root.join(&local_path[..local_path.len()-4])
        } else {
            // 否则直接使用local_path作为目录
            target_path
        };

        println!("解压ZIP文件到: {:?}", extract_dir);
        extract_zip(&file_data, &extract_dir).map_err(|e| format!("解压失败: {}", e))?;
    } else {
        // 其他文件直接保存
        if let Some(parent) = target_path.parent() {
            fs::create_dir_all(parent).map_err(|e| format!("创建目录失败: {}", e))?;
        }
        fs::write(&target_path, &file_data).map_err(|e| format!("写入文件失败: {}", e))?;
        println!("文件已保存到: {:?}", target_path);
    }

    Ok(())
}

fn extract_zip(data: &[u8], target_dir: &Path) -> Result<()> {
    use std::io::Cursor;

    // 创建目标目录
    if target_dir.exists() {
        fs::remove_dir_all(target_dir)?;
    }
    fs::create_dir_all(target_dir)?;

    let cursor = Cursor::new(data);
    let mut archive = zip::ZipArchive::new(cursor)?;

    for i in 0..archive.len() {
        let mut file = archive.by_index(i)?;
        let outpath = target_dir.join(file.name());

        if file.name().ends_with('/') {
            // 目录
            fs::create_dir_all(&outpath)?;
        } else {
            // 文件
            if let Some(parent) = outpath.parent() {
                fs::create_dir_all(parent)?;
            }
            let mut outfile = fs::File::create(&outpath)?;
            std::io::copy(&mut file, &mut outfile)?;
        }
    }

    Ok(())
}

#[tauri::command]
async fn update_local_version(
    component_id: String,
    version: String,
    checksum: String,
    last_updated: String,
) -> Result<(), String> {
    let project_root = get_project_root().map_err(|e| e.to_string())?;
    let config_dir = project_root.join("config");
    let config_path = config_dir.join("local-version.json");

    // 确保配置目录存在
    fs::create_dir_all(&config_dir).map_err(|e| format!("创建配置目录失败: {}", e))?;

    // 读取现有配置或创建新的
    let mut local_version = if config_path.exists() {
        let content = fs::read_to_string(&config_path).map_err(|e| e.to_string())?;
        serde_json::from_str::<LocalVersion>(&content).map_err(|e| e.to_string())?
    } else {
        LocalVersion {
            version: "1.0.0".to_string(),
            last_checked: chrono::Utc::now().to_rfc3339(),
            components: HashMap::new(),
        }
    };

    // 更新组件信息
    local_version.components.insert(
        component_id,
        LocalVersionComponent {
            version,
            checksum,
            last_updated,
        },
    );
    local_version.last_checked = chrono::Utc::now().to_rfc3339();

    // 保存到文件
    let json_content = serde_json::to_string_pretty(&local_version).map_err(|e| e.to_string())?;
    fs::write(&config_path, json_content).map_err(|e| format!("保存配置失败: {}", e))?;

    Ok(())
}

#[tauri::command]
async fn launch_main_application() -> Result<(), String> {
    // 获取更新器的执行路径
    let updater_exe = std::env::current_exe().map_err(|e| format!("获取更新器路径失败: {}", e))?;
    let updater_dir = updater_exe.parent().ok_or("无法获取更新器目录")?;

    // 查找主程序可执行文件的可能位置
    let search_paths = vec![
        // 同级目录
        updater_dir.to_path_buf(),
        // 上级目录
        updater_dir.parent().unwrap_or(updater_dir).to_path_buf(),
        // 主程序目录（如果是onedir打包）
        updater_dir.join("main_app"),
        updater_dir.join("弈秒决"),
        updater_dir.join("YimiaoJue"),
    ];

    let possible_names = [
        "弈秒决.exe",
        "YimiaoJue.exe",
        "TFT助手.exe",
        "main.exe",
        "app.exe"
    ];

    for search_dir in &search_paths {
        for name in &possible_names {
            let exe_path = search_dir.join(name);
            if exe_path.exists() {
                println!("找到主程序: {:?}", exe_path);

                // 启动主程序，设置工作目录为主程序所在目录
                match Command::new(&exe_path)
                    .current_dir(exe_path.parent().unwrap_or(search_dir))
                    .spawn()
                {
                    Ok(_) => {
                        println!("主程序启动成功");
                        return Ok(());
                    }
                    Err(e) => {
                        println!("启动失败: {}", e);
                        continue;
                    }
                }
            }
        }
    }

    Err(format!("未找到主程序可执行文件，搜索路径: {:?}", search_paths))
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![
            get_local_version,
            get_config_from_server,
            get_remote_manifest,
            download_and_update_component,
            update_local_version,
            launch_main_application
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
